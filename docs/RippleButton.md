# RippleButton Component

A custom React Native button component with ripple animation effect, built without external libraries using React Native's Animated API.

## Features

- ✨ **Custom Ripple Animation**: Smooth ripple effect that starts from touch point
- 🎨 **Multiple Variants**: Contained, outlined, and text button styles
- 📏 **Size Options**: Small, medium, and large sizes
- 🎯 **Touch Feedback**: Visual feedback with customizable ripple colors
- ♿ **Accessibility**: Proper disabled states and touch targets
- 🔧 **Customizable**: Custom colors, durations, and content support
- 📱 **Cross-Platform**: Works on both iOS and Android

## Installation

The component is already included in your project at `src/components/common/RippleButton.tsx`.

```tsx
import { RippleButton } from '@components/common/RippleButton';
```

## Basic Usage

```tsx
import React from 'react';
import { RippleButton } from '@components/common/RippleButton';

const MyComponent = () => {
  const handlePress = () => {
    console.log('Button pressed!');
  };

  return (
    <RippleButton
      title="Press Me"
      onPress={handlePress}
    />
  );
};
```

## Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `title` | `string` | - | Button text (required) |
| `onPress` | `() => void` | - | Function called when button is pressed |
| `variant` | `'contained' \| 'outlined' \| 'text'` | `'contained'` | Button style variant |
| `size` | `'small' \| 'medium' \| 'large'` | `'medium'` | Button size |
| `fullWidth` | `boolean` | `false` | Whether button should take full width |
| `disabled` | `boolean` | `false` | Whether button is disabled |
| `loading` | `boolean` | `false` | Whether button is in loading state |
| `style` | `ViewStyle` | - | Custom button styles |
| `textStyle` | `TextStyle` | - | Custom text styles |
| `rippleColor` | `string` | - | Custom ripple color (auto-calculated if not provided) |
| `rippleDuration` | `number` | `600` | Ripple animation duration in milliseconds |
| `children` | `React.ReactNode` | - | Custom content (overrides title) |

## Examples

### Variants

```tsx
// Contained button (default)
<RippleButton
  title="Contained"
  variant="contained"
  onPress={handlePress}
/>

// Outlined button
<RippleButton
  title="Outlined"
  variant="outlined"
  onPress={handlePress}
/>

// Text button
<RippleButton
  title="Text"
  variant="text"
  onPress={handlePress}
/>
```

### Sizes

```tsx
// Small button
<RippleButton
  title="Small"
  size="small"
  onPress={handlePress}
/>

// Medium button (default)
<RippleButton
  title="Medium"
  size="medium"
  onPress={handlePress}
/>

// Large button
<RippleButton
  title="Large"
  size="large"
  onPress={handlePress}
/>
```

### States

```tsx
// Disabled button
<RippleButton
  title="Disabled"
  disabled={true}
  onPress={handlePress}
/>

// Loading button
<RippleButton
  title="Loading"
  loading={true}
  onPress={handlePress}
/>

// Full width button
<RippleButton
  title="Full Width"
  fullWidth={true}
  onPress={handlePress}
/>
```

### Custom Ripple Colors

```tsx
// Red ripple
<RippleButton
  title="Red Ripple"
  rippleColor="rgba(255, 0, 0, 0.3)"
  onPress={handlePress}
/>

// Blue ripple
<RippleButton
  title="Blue Ripple"
  rippleColor="rgba(0, 0, 255, 0.2)"
  onPress={handlePress}
/>
```

### Custom Animation Duration

```tsx
// Fast ripple
<RippleButton
  title="Fast Ripple"
  rippleDuration={300}
  onPress={handlePress}
/>

// Slow ripple
<RippleButton
  title="Slow Ripple"
  rippleDuration={1000}
  onPress={handlePress}
/>
```

### Custom Content

```tsx
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';

// Button with icon
<RippleButton
  title=""
  onPress={handlePress}
>
  <View style={{ flexDirection: 'row', alignItems: 'center' }}>
    <Icon name="heart" size={20} color="white" />
    <Text style={{ color: 'white', marginLeft: 8 }}>Like</Text>
  </View>
</RippleButton>
```

### Custom Styling

```tsx
// Custom button style
<RippleButton
  title="Custom Style"
  onPress={handlePress}
  style={{
    backgroundColor: '#FF6B6B',
    borderRadius: 25,
    paddingHorizontal: 30,
  }}
  textStyle={{
    fontSize: 18,
    fontWeight: 'bold',
  }}
  rippleColor="rgba(255, 255, 255, 0.4)"
/>
```

## How It Works

The RippleButton component creates a ripple effect by:

1. **Touch Detection**: Captures touch coordinates using `GestureResponderEvent`
2. **Ripple Creation**: Creates animated circles at touch points
3. **Animation**: Uses `Animated.parallel()` to animate scale and opacity
4. **Cleanup**: Removes completed ripples from state

The ripple effect calculates the maximum distance from the touch point to button edges to ensure the ripple covers the entire button area.

## Performance Considerations

- Uses `useNativeDriver: true` for optimal performance
- Automatically cleans up completed animations
- Minimal re-renders with efficient state management
- Proper memory management with animation cleanup

## Accessibility

- Supports disabled states with visual feedback
- Proper touch targets for accessibility
- Screen reader compatible
- Keyboard navigation support (when applicable)

## Demo

To see all variants and features in action, check out the demo component:

```tsx
import { RippleButtonDemo } from '@components/common/RippleButtonDemo';
```

This demo showcases all the different variants, sizes, states, and customization options available with the RippleButton component.
