module.exports = {
  root: true,
  extends: ['@react-native', 'eslint:recommended'],
  // parser: '@typescript-eslint/parser',
  parserOptions: {
    ecmaVersion: 2020,
    sourceType: 'module',
  },
  plugins: ['import'],
  rules: {
    'object-curly-spacing': ['error', 'always'],
    // 'react-native/no-inline-styles': 'off',
    // 'react-native/no-color-literals': 'off',
    // 'react-native/no-raw-text': 'off',
    // 'react-native/no-single-element-style-arrays': 'off',
    // 'react-native/sort-styles': 'off',
    // 'react/jsx-filename-extension': [
    //   1,
    //   {
    //     extensions: ['.js', '.jsx'],
    //   },
    // ],
    '@typescript-eslint/no-unused-vars': [
      'warn',
      {argsIgnorePattern: '^_', varsIgnorePattern: '^React$'},
    ],
    'import/order': [
      'warn',
      {
        groups: [
          // Ensure `react` is always first
          'builtin', // e.g. fs, path
          'external', // e.g. react, react-native
          'internal', // your aliases like @components
          'sibling', // e.g. imports from sibling files
        ],
        pathGroups: [],
        pathGroupsExcludedImportTypes: ['builtin'],

        alphabetize: {
          order: 'asc', // Order imports alphabetically
          caseInsensitive: true,
        },
        'newlines-between': 'always', // Ensure a new line between import groups
      },
    ],
  },
  settings: {
    'import/resolver': {
      node: true,
      typescript: {
        project: './tsconfig.json', // Pointing to the custom tsconfig
      },
    },
  },
};
