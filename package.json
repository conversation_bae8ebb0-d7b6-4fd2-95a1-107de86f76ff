{"name": "CrimeLens", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint . --ext .ts,.tsx --fix", "start": "react-native start", "test": "jest"}, "dependencies": {"@react-native-async-storage/async-storage": "^2.1.2", "@react-native-community/geolocation": "^3.4.0", "@react-navigation/bottom-tabs": "^7.3.13", "@react-navigation/native": "^7.1.9", "@react-navigation/native-stack": "^7.3.13", "@types/lodash": "^4.17.17", "axios": "^1.9.0", "lodash": "^4.17.21", "react": "19.0.0", "react-native": "0.79.2", "react-native-date-picker": "^5.0.12", "react-native-gesture-handler": "^2.25.0", "react-native-image-crop-picker": "^0.42.0", "react-native-leaflet-view": "^1.1.1", "react-native-linear-gradient": "^2.8.3", "react-native-maps": "^1.23.8", "react-native-paper": "^5.14.5", "react-native-permissions": "^5.4.0", "react-native-reanimated": "^3.17.5", "react-native-safe-area-context": "^5.4.1", "react-native-screens": "^4.10.0", "react-native-toast-message": "^2.3.0", "react-native-vector-icons": "^10.2.0", "react-native-webview": "^13.13.5", "zustand": "^5.0.5"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/preset-env": "^7.25.3", "@babel/runtime": "^7.25.0", "@react-native-community/cli": "18.0.0", "@react-native-community/cli-platform-android": "18.0.0", "@react-native-community/cli-platform-ios": "18.0.0", "@react-native/babel-preset": "0.79.2", "@react-native/eslint-config": "0.79.2", "@react-native/metro-config": "0.79.2", "@react-native/typescript-config": "0.79.2", "@types/jest": "^29.5.13", "@types/react": "^19.0.0", "@types/react-native": "^0.72.8", "@types/react-native-vector-icons": "^6.4.18", "@types/react-test-renderer": "^19.0.0", "babel-plugin-module-resolver": "^5.0.2", "eslint": "^8.19.0", "eslint-plugin-import": "^2.31.0", "jest": "^29.6.3", "prettier": "2.8.8", "react-native-dotenv": "^3.4.11", "react-test-renderer": "19.0.0", "typescript": "5.0.4"}, "engines": {"node": ">=18"}}