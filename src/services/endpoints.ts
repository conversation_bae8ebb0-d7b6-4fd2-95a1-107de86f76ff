import {
  ApiResponse,
  LoginRequest,
  VerifyOTPRequest,
  ProfileSetupRequest,
  AddressRequest,
  User,
} from '@types';

import { apiClient } from './api';

// Auth Endpoints
export const authAPI = {
  login: (data: LoginRequest): Promise<ApiResponse<{ token: string }>> =>
    apiClient.post('/auth/login', data),

  resendOTP: (data: { mobile: string }): Promise<ApiResponse<any>> =>
    apiClient.post('/auth/resend-otp', data),

  verifyOTP: (data: VerifyOTPRequest): Promise<ApiResponse<{ token: string; user: User }>> =>
    apiClient.post('/auth/verify-otp', data),
};

// User Endpoints
export const userAPI = {
  profileSetup: (data: ProfileSetupRequest): Promise<ApiResponse<User>> =>
    apiClient.post('/user/profile-setup', data),

  uploadProfilePicture: (formData: FormData): Promise<ApiResponse<{ profile_picture: string }>> =>
    apiClient.upload('/user/profile-picture', formData),

  updateAddress: (data: AddressRequest): Promise<ApiResponse<User>> =>
    apiClient.put('/user/address', data),

  getProfile: (): Promise<ApiResponse<User>> =>
    apiClient.get('/user/profile'),
};

// Google Places API
export const placesAPI = {
  autocomplete: async (input: string): Promise<any> => {
    const response = await fetch(
      `https://maps.googleapis.com/maps/api/place/autocomplete/json?input=${encodeURIComponent(
        input,
      )}&key=AIzaSyBXb6rhST1LONjkKeEeK6OnuQpcdoCxqSg`,
    );
    return response.json();
  },

  getPlaceDetails: async (placeId: string): Promise<any> => {
    const response = await fetch(
      `https://maps.googleapis.com/maps/api/place/details/json?place_id=${placeId}&key=AIzaSyBXb6rhST1LONjkKeEeK6OnuQpcdoCxqSg`,
    );
    return response.json();
  },
};
