import {
  ApiResponse,
  LoginRequest,
  VerifyOTPRequest,
  ProfileSetupRequest,
  AddressRequest,
  User,
  CrimeSearchParams,
  CrimeSearchResponse,
} from '@types';

import { apiClient } from './api';

// Auth Endpoints
export const authAPI = {
  login: (data: LoginRequest): Promise<ApiResponse<{ token: string }>> =>
    apiClient.post('/auth/login', data),

  resendOTP: (data: { mobile: string }): Promise<ApiResponse<any>> =>
    apiClient.post('/auth/resend-otp', data),

  verifyOTP: (
    data: VerifyOTPRequest,
  ): Promise<ApiResponse<{ token: string; user: User }>> =>
    apiClient.post('/auth/verify-otp', data),
};

// User Endpoints
export const userAPI = {
  profileSetup: (data: ProfileSetupRequest): Promise<ApiResponse<User>> =>
    apiClient.post('/user/profile-setup', data),

  uploadProfilePicture: (
    formData: FormData,
  ): Promise<ApiResponse<{ profile_picture: string }>> =>
    apiClient.upload('/user/profile-picture', formData),

  updateAddress: (data: AddressRequest): Promise<ApiResponse<User>> =>
    apiClient.put('/user/address', data),

  getProfile: (): Promise<ApiResponse<User>> => apiClient.get('/user/profile'),
};

// Crime Data API
export const crimeAPI = {
  searchCrimes: (
    params: CrimeSearchParams,
  ): Promise<ApiResponse<CrimeSearchResponse>> => {
    // Build query parameters
    const queryParams: Record<string, string> = {};

    if (params.crime_type) queryParams.crime_type = params.crime_type;
    if (params.province) queryParams.province = params.province;
    if (params.city) queryParams.city = params.city;
    if (params.area) queryParams.area = params.area;
    if (params.from_date) queryParams.from_date = params.from_date;
    if (params.to_date) queryParams.to_date = params.to_date;
    if (params.severity) queryParams.severity = params.severity;
    if (params.status) queryParams.status = params.status;
    if (params.page) queryParams.page = params.page.toString();
    if (params.limit) queryParams.limit = params.limit.toString();

    return apiClient.get('/crime/search', queryParams);
  },

  getCrimeById: (id: string): Promise<ApiResponse<any>> =>
    apiClient.get(`/crime/${id}`),

  reportCrime: (data: any): Promise<ApiResponse<any>> =>
    apiClient.post('/crime/report', data),
};

// Google Places API
export const placesAPI = {
  autocomplete: async (input: string): Promise<any> => {
    const response = await fetch(
      `https://maps.googleapis.com/maps/api/place/autocomplete/json?input=${encodeURIComponent(
        input,
      )}&key=AIzaSyBXb6rhST1LONjkKeEeK6OnuQpcdoCxqSg`,
    );
    return response.json();
  },

  getPlaceDetails: async (placeId: string): Promise<any> => {
    const response = await fetch(
      `https://maps.googleapis.com/maps/api/place/details/json?place_id=${placeId}&key=AIzaSyBXb6rhST1LONjkKeEeK6OnuQpcdoCxqSg`,
    );
    return response.json();
  },
};
