import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>utton, PickerModal } from '@components/common';
import { NavigationProp, RouteProp } from '@react-navigation/native';
import { CRIMETYPE } from '@utils/constant';
import { Colors, Spacing } from '@utils/theme';
import { Typography } from '@utils/typography';
import React, { useState, useEffect, useRef } from 'react';
import { View, StyleSheet, ScrollView, Animated, Alert } from 'react-native';
import DatePicker from 'react-native-date-picker';
import LinearGradient from 'react-native-linear-gradient';
import { Text } from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';

import { HomeStackParamList, FilterData, CityData } from '@/types';

const canadaCitiesData = require('@data/canadaCities.json');

type FilterScreenNavigationProp = NavigationProp<HomeStackParamList, 'Filter'>;
type FilterScreenRouteProp = RouteProp<HomeStackParamList, 'Filter'>;

interface Props {
  navigation: FilterScreenNavigationProp;
  route: FilterScreenRouteProp;
}

const FilterScreen: React.FC<Props> = ({ navigation, route }) => {
  const { onFilterApply } = route.params;

  const [filters, setFilters] = useState<FilterData>({
    fromDate: '',
    toDate: '',
    crimeType: '',
    city: '',
    area: '',
  });

  const [showFromDatePicker, setShowFromDatePicker] = useState(false);
  const [showToDatePicker, setShowToDatePicker] = useState(false);
  const [showCrimeTypePicker, setShowCrimeTypePicker] = useState(false);
  const [showCityPicker, setShowCityPicker] = useState(false);
  const [showAreaPicker, setShowAreaPicker] = useState(false);

  const [availableAreas, setAvailableAreas] = useState<string[]>([]);
  const [allCities, setAllCities] = useState<string[]>([]);

  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(50)).current;

  const cityData: CityData = canadaCitiesData;

  useEffect(() => {
    // Extract all cities from the data
    const cities: string[] = [];
    Object.values(cityData).forEach(province => {
      Object.keys(province).forEach(city => {
        cities.push(city);
      });
    });
    setAllCities(cities.sort());

    console.log('Cities loaded:', cities.length);
    console.log('Crime types available:', CRIMETYPE.length);

    // Animate screen entrance
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 600,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 600,
        useNativeDriver: true,
      }),
    ]).start();
  }, [fadeAnim, slideAnim, cityData]);

  useEffect(() => {
    // Update available areas when city changes
    if (filters.city) {
      let areas: string[] = [];
      Object.values(cityData).forEach(province => {
        if (province[filters.city]) {
          areas = province[filters.city];
        }
      });
      setAvailableAreas(areas);
      console.log('Areas loaded for', filters.city, ':', areas.length);

      // Reset area if it's not available in the new city
      if (filters.area && !areas.includes(filters.area)) {
        setFilters(prev => ({ ...prev, area: '' }));
      }
    } else {
      setAvailableAreas([]);
      setFilters(prev => ({ ...prev, area: '' }));
    }
  }, [filters.city, filters.area, cityData]);

  const formatDate = (date: Date): string => {
    return date.toISOString().split('T')[0];
  };

  const handleDateChange = (date: Date, type: 'from' | 'to') => {
    const formattedDate = formatDate(date);
    if (type === 'from') {
      setFilters(prev => ({ ...prev, fromDate: formattedDate }));
      setShowFromDatePicker(false);
    } else {
      setFilters(prev => ({ ...prev, toDate: formattedDate }));
      setShowToDatePicker(false);
    }
  };

  const validateFilters = (): boolean => {
    if (!filters.fromDate || !filters.toDate) {
      Alert.alert('Error', 'Please select both from and to dates');
      return false;
    }

    if (new Date(filters.fromDate) > new Date(filters.toDate)) {
      Alert.alert('Error', 'From date cannot be later than to date');
      return false;
    }

    if (!filters.crimeType) {
      Alert.alert('Error', 'Please select a crime type');
      return false;
    }

    if (!filters.city) {
      Alert.alert('Error', 'Please select a city');
      return false;
    }

    if (!filters.area) {
      Alert.alert('Error', 'Please select an area');
      return false;
    }

    return true;
  };

  const handleApplyFilters = () => {
    if (validateFilters()) {
      onFilterApply(filters);
      navigation.goBack();
    }
  };

  const handleReset = () => {
    setFilters({
      fromDate: '',
      toDate: '',
      crimeType: '',
      city: '',
      area: '',
    });
  };

  const renderPickerButton = (
    label: string,
    value: string,
    onPress: () => void,
    placeholder: string,
  ) => {
    const buttonStyle = {
      ...styles.pickerButton,
      ...(value ? {} : styles.placeholderButton),
    };

    const textStyle = {
      ...styles.pickerButtonText,
      ...(value ? {} : styles.placeholderText),
    };

    return (
      <View style={styles.fieldContainer}>
        <Text style={styles.fieldLabel}>{label}</Text>
        <RippleButton
          title={value || placeholder}
          onPress={onPress}
          variant="outlined"
          style={buttonStyle}
          textStyle={textStyle}
        />
      </View>
    );
  };

  return (
    <View style={styles.container}>
      <LinearGradient
        colors={['#1976D2', '#1565C0', '#0D47A1']}
        style={styles.backgroundGradient}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      />

      <Header
        title="Filter Crime Data"
        showBackButton
        onBackPress={() => navigation.goBack()}
        // style={styles.header}
      />

      <Animated.View
        style={[
          styles.content,
          {
            opacity: fadeAnim,
            transform: [{ translateY: slideAnim }],
          },
        ]}>
        <ScrollView
          style={styles.scrollView}
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}>
          {/* Date Range Section */}
          <View style={styles.section}>
            <View style={styles.sectionHeader}>
              <Icon name="calendar-range" size={24} color={Colors.white} />
              <Text style={styles.sectionTitle}>Date Range</Text>
            </View>

            <View style={styles.dateRow}>
              {renderPickerButton(
                'From Date',
                filters.fromDate,
                () => setShowFromDatePicker(true),
                'Select start date',
              )}

              {renderPickerButton(
                'To Date',
                filters.toDate,
                () => setShowToDatePicker(true),
                'Select end date',
              )}
            </View>
          </View>

          {/* Crime Type Section */}
          <View style={styles.section}>
            <View style={styles.sectionHeader}>
              <Icon name="shield-alert" size={24} color={Colors.white} />
              <Text style={styles.sectionTitle}>Crime Type</Text>
            </View>

            {renderPickerButton(
              'Crime Type',
              filters.crimeType,
              () => setShowCrimeTypePicker(true),
              'Select crime type',
            )}
          </View>

          {/* Location Section */}
          <View style={styles.section}>
            <View style={styles.sectionHeader}>
              <Icon name="map-marker" size={24} color={Colors.white} />
              <Text style={styles.sectionTitle}>Location</Text>
            </View>

            {renderPickerButton(
              'City',
              filters.city,
              () => setShowCityPicker(true),
              'Select city',
            )}

            {renderPickerButton(
              'Area',
              filters.area,
              () => setShowAreaPicker(true),
              filters.city ? 'Select area' : 'Select city first',
            )}
          </View>

          {/* Action Buttons */}
          <View style={styles.buttonContainer}>
            <RippleButton
              title="Reset"
              onPress={handleReset}
              variant="outlined"
              style={styles.resetButton}
              textStyle={styles.resetButtonText}
            />

            <RippleButton
              title="Apply Filters"
              onPress={handleApplyFilters}
              variant="contained"
              style={styles.applyButton}
              textStyle={styles.applyButtonText}
            />
          </View>
        </ScrollView>
      </Animated.View>

      {/* Date Pickers */}
      <DatePicker
        modal
        open={showFromDatePicker}
        date={filters.fromDate ? new Date(filters.fromDate) : new Date()}
        mode="date"
        onConfirm={date => handleDateChange(date, 'from')}
        onCancel={() => setShowFromDatePicker(false)}
      />

      <DatePicker
        modal
        open={showToDatePicker}
        date={filters.toDate ? new Date(filters.toDate) : new Date()}
        mode="date"
        onConfirm={date => handleDateChange(date, 'to')}
        onCancel={() => setShowToDatePicker(false)}
      />

      {/* Crime Type Picker */}
      <PickerModal
        visible={showCrimeTypePicker}
        title="Select Crime Type"
        options={CRIMETYPE}
        selectedValue={filters.crimeType}
        onSelect={value => setFilters(prev => ({ ...prev, crimeType: value }))}
        onClose={() => setShowCrimeTypePicker(false)}
      />

      {/* City Picker */}
      <PickerModal
        visible={showCityPicker}
        title="Select City"
        options={allCities}
        selectedValue={filters.city}
        onSelect={value => setFilters(prev => ({ ...prev, city: value }))}
        onClose={() => setShowCityPicker(false)}
      />

      {/* Area Picker */}
      <PickerModal
        visible={showAreaPicker}
        title="Select Area"
        options={availableAreas}
        selectedValue={filters.area}
        onSelect={value => setFilters(prev => ({ ...prev, area: value }))}
        onClose={() => setShowAreaPicker(false)}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  backgroundGradient: {
    ...StyleSheet.absoluteFillObject,
  },
  header: {
    backgroundColor: 'transparent',
  },
  content: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: Spacing.lg,
    paddingBottom: Spacing.xl * 2,
  },
  section: {
    marginBottom: Spacing.xl,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Spacing.md,
  },
  sectionTitle: {
    ...Typography.h3,
    color: Colors.white,
    marginLeft: Spacing.sm,
    fontSize: 18,
    fontWeight: '600',
  },
  fieldContainer: {
    marginBottom: Spacing.md,
  },
  fieldLabel: {
    ...Typography.body2,
    color: Colors.white,
    marginBottom: Spacing.xs,
    fontSize: 14,
    fontWeight: '500',
  },
  dateRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: Spacing.md,
  },
  pickerButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderColor: 'rgba(255, 255, 255, 0.3)',
    borderWidth: 1,
    paddingVertical: Spacing.md,
    paddingHorizontal: Spacing.lg,
    borderRadius: 12,
    flex: 1,
  },
  placeholderButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
  },
  pickerButtonText: {
    color: Colors.white,
    fontSize: 14,
    fontWeight: '500',
  },
  placeholderText: {
    opacity: 0.7,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: Spacing.md,
    marginTop: Spacing.xl,
  },
  resetButton: {
    flex: 1,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderColor: Colors.white,
    borderWidth: 1,
  },
  resetButtonText: {
    color: Colors.white,
    fontWeight: '600',
  },
  applyButton: {
    flex: 1,
    backgroundColor: Colors.white,
  },
  applyButtonText: {
    color: Colors.primary,
    fontWeight: '600',
  },
});

export default FilterScreen;
