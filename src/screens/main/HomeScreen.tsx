import { RippleButton } from '@components/common';
import { useNavigation } from '@react-navigation/native';
import { NavigationProp } from '@react-navigation/native';
import { Colors, Spacing } from '@utils/theme';
import { Typography } from '@utils/typography';
import React, { useEffect, useRef, useState, useCallback } from 'react';
import {
  StyleSheet,
  View,
  Animated,
  Dimensions,
  TouchableOpacity,
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import { Text } from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';

import {
  performCrimeSearch,
  getBasicCrimeData,
  getCurrentAuthToken,
} from '../../actions/crimeActions';
import { useCrimeStore } from '../../store';
import { HomeStackParamList, FilterData, CrimeRecord } from '@/types';

const { width, height } = Dimensions.get('window');

const HomeScreen: React.FC = () => {
  const navigation = useNavigation<NavigationProp<HomeStackParamList>>();
  const [appliedFilters, setAppliedFilters] = useState<FilterData | null>(null);

  // Crime store
  const {
    crimes,
    isLoading,
    error,
    totalRecords,
    hasActiveFilters,
    setCrimes,
    setLoading,
    setError,
    setAppliedFilters: setStoreFilters,
  } = useCrimeStore();

  const iconOpacity = useRef(new Animated.Value(0)).current;
  const iconScale = useRef(new Animated.Value(0.5)).current;
  const titleOpacity = useRef(new Animated.Value(0)).current;
  const titleTranslateY = useRef(new Animated.Value(30)).current;
  const subtitleOpacity = useRef(new Animated.Value(0)).current;
  const subtitleTranslateY = useRef(new Animated.Value(30)).current;
  const buttonOpacity = useRef(new Animated.Value(0)).current;
  const buttonTranslateY = useRef(new Animated.Value(30)).current;

  useEffect(() => {
    // Staggered entrance animations
    Animated.sequence([
      Animated.parallel([
        Animated.timing(iconOpacity, {
          toValue: 1,
          duration: 800,
          useNativeDriver: true,
        }),
        Animated.spring(iconScale, {
          toValue: 1,
          tension: 50,
          friction: 7,
          useNativeDriver: true,
        }),
      ]),
      Animated.parallel([
        Animated.timing(titleOpacity, {
          toValue: 1,
          duration: 600,
          useNativeDriver: true,
        }),
        Animated.timing(titleTranslateY, {
          toValue: 0,
          duration: 600,
          useNativeDriver: true,
        }),
      ]),
      Animated.parallel([
        Animated.timing(subtitleOpacity, {
          toValue: 1,
          duration: 600,
          useNativeDriver: true,
        }),
        Animated.timing(subtitleTranslateY, {
          toValue: 0,
          duration: 600,
          useNativeDriver: true,
        }),
      ]),
      Animated.parallel([
        Animated.timing(buttonOpacity, {
          toValue: 1,
          duration: 600,
          useNativeDriver: true,
        }),
        Animated.timing(buttonTranslateY, {
          toValue: 0,
          duration: 600,
          useNativeDriver: true,
        }),
      ]),
    ]).start();
  }, []);

  const loadInitialData = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      // Verify authentication token
      const token = await getCurrentAuthToken();
      if (!token) {
        setError('Authentication required');
        setLoading(false);
        return;
      }

      const result = await getBasicCrimeData(10);
      if (result) {
        setCrimes(result.data);
        console.log(
          '✅ Initial crime data loaded:',
          result.data.length,
          'records',
        );
      }
    } catch (err: any) {
      console.error('❌ Failed to load initial data:', err);
      setError('Failed to load crime data');
    } finally {
      setLoading(false);
    }
  }, [setCrimes, setLoading, setError]);

  // Load initial crime data
  useEffect(() => {
    loadInitialData();
  }, [loadInitialData]);

  const handleFilterPress = () => {
    navigation.navigate('Filter', {
      onFilterApply: async (filters: FilterData) => {
        setAppliedFilters(filters);
        setStoreFilters(filters);

        console.log('🔍 Applying filters:', filters);

        // Fetch filtered crime data
        setLoading(true);
        setError(null);

        try {
          const result = await performCrimeSearch(filters, { limit: 20 });
          if (result) {
            setCrimes(result.data);
            console.log(
              '✅ Filtered crime data loaded:',
              result.data.length,
              'records',
            );
          }
        } catch (err: any) {
          console.error('❌ Failed to load filtered data:', err);
          setError('Failed to load filtered crime data');
        } finally {
          setLoading(false);
        }
      },
    });
  };

  const handleClearFilters = async () => {
    setAppliedFilters(null);
    setStoreFilters(null);

    // Reload initial data
    await loadInitialData();
  };

  return (
    <View style={styles.container}>
      <LinearGradient
        colors={['#1976D2', '#1565C0', '#0D47A1']}
        style={styles.backgroundGradient}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      />

      <View style={styles.content}>
        <Animated.View
          style={[
            styles.iconContainer,
            {
              opacity: iconOpacity,
              transform: [{ scale: iconScale }],
            },
          ]}>
          <Icon name="home" size={80} color={Colors.primary} />
          <View style={styles.iconGlow} />
        </Animated.View>

        <Animated.View
          style={{
            opacity: titleOpacity,
            transform: [{ translateY: titleTranslateY }],
          }}>
          <Text style={styles.title}>Home / Location</Text>
        </Animated.View>

        <Animated.View
          style={[
            styles.subtitleContainer,
            {
              opacity: subtitleOpacity,
              transform: [{ translateY: subtitleTranslateY }],
            },
          ]}>
          <Text style={styles.subtitle}>
            View crime data for your area. Use filters to search specific crime
            types, locations, and date ranges across Canada.
          </Text>

          {/* Crime Data Stats */}
          {totalRecords > 0 && (
            <Text style={styles.statsText}>
              {totalRecords} crime records available
            </Text>
          )}
          {isLoading && (
            <Text style={styles.loadingText}>Loading crime data...</Text>
          )}
          {error && <Text style={styles.errorText}>{error}</Text>}
        </Animated.View>

        {/* Applied Filters Display */}
        {appliedFilters && (
          <Animated.View
            style={[
              styles.filtersContainer,
              {
                opacity: buttonOpacity,
                transform: [{ translateY: buttonTranslateY }],
              },
            ]}>
            <View style={styles.filtersHeader}>
              <Text style={styles.filtersTitle}>Applied Filters</Text>
              <TouchableOpacity
                onPress={handleClearFilters}
                style={styles.clearButton}>
                <Icon name="close-circle" size={20} color={Colors.white} />
              </TouchableOpacity>
            </View>
            <View style={styles.filterTags}>
              <View style={styles.filterTag}>
                <Text style={styles.filterTagText}>
                  {appliedFilters.fromDate} - {appliedFilters.toDate}
                </Text>
              </View>
              <View style={styles.filterTag}>
                <Text style={styles.filterTagText}>
                  {appliedFilters.crimeType}
                </Text>
              </View>
              <View style={styles.filterTag}>
                <Text style={styles.filterTagText}>
                  {appliedFilters.city}, {appliedFilters.area}
                </Text>
              </View>
            </View>
          </Animated.View>
        )}

        {/* Filter Button */}
        <Animated.View
          style={[
            styles.buttonContainer,
            {
              opacity: buttonOpacity,
              transform: [{ translateY: buttonTranslateY }],
            },
          ]}>
          <RippleButton
            title={appliedFilters ? 'Update Filters' : 'Filter Crime Data'}
            onPress={handleFilterPress}
            variant="contained"
            style={styles.filterButton}
            textStyle={styles.filterButtonText}
          />
        </Animated.View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  backgroundGradient: {
    ...StyleSheet.absoluteFillObject,
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: Spacing.lg,
  },
  iconContainer: {
    width: 160,
    height: 160,
    borderRadius: 80,
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: Spacing.xl,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 10 },
    shadowOpacity: 0.3,
    shadowRadius: 20,
    elevation: 15,
  },
  iconGlow: {
    position: 'absolute',
    width: 140,
    height: 140,
    borderRadius: 70,
    backgroundColor: 'rgba(25, 118, 210, 0.2)',
    shadowColor: Colors.primary,
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0.6,
    shadowRadius: 30,
    elevation: 10,
  },
  title: {
    ...Typography.h2,
    color: Colors.white,
    marginTop: Spacing.lg,
    marginBottom: Spacing.sm,
    textAlign: 'center',
    fontSize: 32,
    fontWeight: '700',
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 0, height: 2 },
    textShadowRadius: 8,
  },
  subtitleContainer: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 20,
    padding: Spacing.lg,
    marginTop: Spacing.md,
  },
  subtitle: {
    ...Typography.body1,
    color: Colors.white,
    textAlign: 'center',
    lineHeight: 24,
    fontSize: 16,
    opacity: 0.9,
    textShadowColor: 'rgba(0, 0, 0, 0.2)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 4,
  },
  filtersContainer: {
    backgroundColor: 'rgba(255, 255, 255, 0.15)',
    borderRadius: 16,
    padding: Spacing.md,
    marginTop: Spacing.lg,
    width: '100%',
  },
  filtersHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Spacing.sm,
  },
  filtersTitle: {
    ...Typography.h4,
    color: Colors.white,
    fontSize: 16,
    fontWeight: '600',
  },
  clearButton: {
    padding: Spacing.xs,
  },
  filterTags: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: Spacing.xs,
  },
  filterTag: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    paddingHorizontal: Spacing.sm,
    paddingVertical: Spacing.xs,
    borderRadius: 12,
    marginRight: Spacing.xs,
    marginBottom: Spacing.xs,
  },
  filterTagText: {
    ...Typography.caption,
    color: Colors.white,
    fontSize: 12,
    fontWeight: '500',
  },
  buttonContainer: {
    marginTop: Spacing.xl,
    width: '100%',
    alignItems: 'center',
  },
  filterButton: {
    backgroundColor: Colors.white,
    paddingHorizontal: Spacing.xl,
    paddingVertical: Spacing.md,
    minWidth: 200,
  },
  filterButtonText: {
    color: Colors.primary,
    fontWeight: '600',
    fontSize: 16,
  },
  statsText: {
    ...Typography.caption,
    color: Colors.white,
    textAlign: 'center',
    marginTop: Spacing.sm,
    fontSize: 14,
    fontWeight: '500',
    opacity: 0.8,
  },
  loadingText: {
    ...Typography.caption,
    color: Colors.white,
    textAlign: 'center',
    marginTop: Spacing.sm,
    fontSize: 14,
    fontStyle: 'italic',
    opacity: 0.8,
  },
  errorText: {
    ...Typography.caption,
    color: '#FF6B6B',
    textAlign: 'center',
    marginTop: Spacing.sm,
    fontSize: 14,
    fontWeight: '500',
  },
});

export default HomeScreen;
