import { AppLogo } from '@assets/images';
import { RippleButton } from '@components/common';
import { NavigationProp } from '@react-navigation/native';
import { RootStackParamList } from '@types';
import { Colors, Spacing } from '@utils/theme';
import { Typography } from '@utils/typography';
import React, { useEffect, useRef } from 'react';
import { Dimensions, StyleSheet, View, Image, Animated } from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import { Text } from 'react-native-paper';

type GetStartedScreenNavigationProp = NavigationProp<
  RootStackParamList,
  'GetStarted'
>;

interface Props {
  navigation: GetStartedScreenNavigationProp;
}

const { width, height } = Dimensions.get('window');

const GetStartedScreen: React.FC<Props> = ({ navigation }) => {
  // Animation values
  const logoScale = useRef(new Animated.Value(0.8)).current;
  const logoOpacity = useRef(new Animated.Value(0)).current;
  const titleOpacity = useRef(new Animated.Value(0)).current;
  const titleTranslateY = useRef(new Animated.Value(30)).current;
  const descriptionOpacity = useRef(new Animated.Value(0)).current;
  const descriptionTranslateY = useRef(new Animated.Value(30)).current;
  const buttonOpacity = useRef(new Animated.Value(0)).current;
  const buttonTranslateY = useRef(new Animated.Value(50)).current;

  useEffect(() => {
    // Staggered animations for smooth entrance
    Animated.sequence([
      // Logo animation
      Animated.parallel([
        Animated.spring(logoScale, {
          toValue: 1,
          tension: 50,
          friction: 7,
          useNativeDriver: true,
        }),
        Animated.timing(logoOpacity, {
          toValue: 1,
          duration: 800,
          useNativeDriver: true,
        }),
      ]),
      // Title animation
      Animated.parallel([
        Animated.timing(titleOpacity, {
          toValue: 1,
          duration: 600,
          useNativeDriver: true,
        }),
        Animated.timing(titleTranslateY, {
          toValue: 0,
          duration: 600,
          useNativeDriver: true,
        }),
      ]),
      // Description animation
      Animated.parallel([
        Animated.timing(descriptionOpacity, {
          toValue: 1,
          duration: 600,
          useNativeDriver: true,
        }),
        Animated.timing(descriptionTranslateY, {
          toValue: 0,
          duration: 600,
          useNativeDriver: true,
        }),
      ]),
      // Button animation
      Animated.parallel([
        Animated.timing(buttonOpacity, {
          toValue: 1,
          duration: 600,
          useNativeDriver: true,
        }),
        Animated.timing(buttonTranslateY, {
          toValue: 0,
          duration: 600,
          useNativeDriver: true,
        }),
      ]),
    ]).start();
  }, []);

  const handleLoginPress = () => {
    navigation.navigate('Login');
  };

  return (
    <View style={styles.container}>
      {/* Enhanced gradient background */}
      <LinearGradient
        colors={['#1976D2', '#1565C0', '#0D47A1', '#0A2E5C']}
        style={styles.backgroundGradient}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      />

      <View style={styles.content}>
        <View style={styles.logoSection}>
          <Animated.View
            style={[
              styles.logoContainer,
              {
                transform: [{ scale: logoScale }],
                opacity: logoOpacity,
              },
            ]}>
            <Image source={AppLogo} style={styles.logoImage} />
            <View style={styles.logoGlow} />
          </Animated.View>

          <Animated.View
            style={{
              opacity: titleOpacity,
              transform: [{ translateY: titleTranslateY }],
            }}>
            <Text style={styles.appName}>Crime Lens</Text>
            <Text style={styles.subtitle}>Stay Safe, Stay Informed</Text>
          </Animated.View>
        </View>

        <Animated.View
          style={[
            styles.descriptionSection,
            {
              opacity: descriptionOpacity,
              transform: [{ translateY: descriptionTranslateY }],
            },
          ]}>
          <Text style={styles.description}>
            Your personal safety companion that helps you stay aware of your
            surroundings and connect with emergency services when needed.
          </Text>
        </Animated.View>

        <Animated.View
          style={[
            styles.buttonSection,
            {
              opacity: buttonOpacity,
              transform: [{ translateY: buttonTranslateY }],
            },
          ]}>
          <RippleButton
            title="Get Started"
            variant="outlined"
            onPress={handleLoginPress}
            style={styles.loginButton}
            textStyle={styles.loginButtonText}
          />
        </Animated.View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  backgroundGradient: {
    ...StyleSheet.absoluteFillObject,
  },
  content: {
    flex: 1,
    paddingHorizontal: Spacing.lg,
    justifyContent: 'space-between',
    paddingTop: height * 0.12,
    paddingBottom: Spacing.xl,
  },
  logoSection: {
    alignItems: 'center',
    flex: 1,
    justifyContent: 'center',
  },
  logoContainer: {
    width: 180,
    height: 180,
    borderRadius: 90,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: Spacing.xl,
    backgroundColor: 'rgba(255, 255, 255, 0.15)',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 10 },
    shadowOpacity: 0.4,
    shadowRadius: 25,
    elevation: 20,
  },
  logoImage: {
    width: 140,
    height: 140,
    borderRadius: 70,
    zIndex: 2,
  },
  logoGlow: {
    position: 'absolute',
    width: 160,
    height: 160,
    borderRadius: 80,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    shadowColor: '#fff',
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0.6,
    shadowRadius: 40,
    elevation: 15,
  },
  appName: {
    ...Typography.h1,
    color: Colors.white,
    marginBottom: Spacing.sm,
    textAlign: 'center',
    fontSize: 52,
    fontWeight: '800',
    textShadowColor: 'rgba(0, 0, 0, 0.4)',
    textShadowOffset: { width: 0, height: 3 },
    textShadowRadius: 10,
  },
  subtitle: {
    ...Typography.h5,
    color: Colors.white,
    opacity: 0.95,
    textAlign: 'center',
    fontSize: 20,
    fontWeight: '500',
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 0, height: 2 },
    textShadowRadius: 6,
  },
  descriptionSection: {
    paddingHorizontal: Spacing.md,
    marginBottom: Spacing.xl,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 20,
    padding: Spacing.lg,
    backdropFilter: 'blur(10px)',
  },
  description: {
    ...Typography.body1,
    color: Colors.white,
    textAlign: 'center',
    opacity: 0.95,
    lineHeight: 26,
    fontSize: 16,
    fontWeight: '400',
  },
  buttonSection: {
    paddingBottom: Spacing.lg,
    paddingHorizontal: Spacing.md,
  },
  loginButton: {
    backgroundColor: Colors.white,
    borderRadius: 16,
    paddingVertical: Spacing.md,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 6 },
    shadowOpacity: 0.3,
    shadowRadius: 15,
    elevation: 12,
  },
  loginButtonText: {
    color: Colors.primary,
    fontSize: 18,
    fontWeight: '600',
  },
});

export default GetStartedScreen;
