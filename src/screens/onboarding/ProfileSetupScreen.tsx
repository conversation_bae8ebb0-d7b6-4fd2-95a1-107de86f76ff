import { Header, Input, RippleButton } from '@components/common';
import { NavigationProp } from '@react-navigation/native';
import { userAPI } from '@services/endpoints';
import { useAuthStore } from '@store/authStore';
import { ProfileForm, RootStackParamList } from '@types';
import { Colors, Spacing } from '@utils/theme';
import { Typography } from '@utils/typography';
import { ValidationRules, validateField } from '@utils/validation';
import React, { useState, useEffect, useRef } from 'react';
import {
  ScrollView,
  StyleSheet,
  View,
  Animated,
  Dimensions,
} from 'react-native';
import { Text } from 'react-native-paper';
import Toast from 'react-native-toast-message';
import LinearGradient from 'react-native-linear-gradient';

type ProfileSetupScreenNavigationProp = NavigationProp<
  RootStackParamList,
  'ProfileSetup'
>;

interface Props {
  navigation: ProfileSetupScreenNavigationProp;
}

const { width, height } = Dimensions.get('window');

const ProfileSetupScreen: React.FC<Props> = ({ navigation }) => {
  const { setUser, setLoading, isLoading, logout } = useAuthStore();
  const [form, setForm] = useState<ProfileForm>({
    firstName: { value: '', error: undefined, touched: false },
    lastName: { value: '', error: undefined, touched: false },
    email: { value: '', error: undefined, touched: false },
    birthDate: { value: '', error: undefined, touched: false },
  });

  // Animation values
  const headerOpacity = useRef(new Animated.Value(0)).current;
  const headerTranslateY = useRef(new Animated.Value(-30)).current;
  const formOpacity = useRef(new Animated.Value(0)).current;
  const formTranslateY = useRef(new Animated.Value(50)).current;

  useEffect(() => {
    // Staggered entrance animations
    Animated.sequence([
      Animated.parallel([
        Animated.timing(headerOpacity, {
          toValue: 1,
          duration: 800,
          useNativeDriver: true,
        }),
        Animated.timing(headerTranslateY, {
          toValue: 0,
          duration: 800,
          useNativeDriver: true,
        }),
      ]),
      Animated.parallel([
        Animated.timing(formOpacity, {
          toValue: 1,
          duration: 600,
          useNativeDriver: true,
        }),
        Animated.timing(formTranslateY, {
          toValue: 0,
          duration: 600,
          useNativeDriver: true,
        }),
      ]),
    ]).start();
  }, []);

  const updateField = (field: keyof ProfileForm, value: string) => {
    setForm(prev => ({
      ...prev,
      [field]: {
        ...prev[field],
        value,
        touched: true,
        error: undefined,
      },
    }));
  };

  const validateForm = (): boolean => {
    const firstNameError = validateField(form.firstName.value, [
      ValidationRules.required,
      ValidationRules.name,
    ]);

    const lastNameError = validateField(form.lastName.value, [
      ValidationRules.required,
      ValidationRules.name,
    ]);

    const emailError = validateField(form.email.value, [
      ValidationRules.required,
      ValidationRules.email,
    ]);

    const birthDateError = validateField(form.birthDate.value, [
      ValidationRules.required,
      ValidationRules.date,
    ]);

    setForm(prev => ({
      ...prev,
      firstName: { ...prev.firstName, error: firstNameError },
      lastName: { ...prev.lastName, error: lastNameError },
      email: { ...prev.email, error: emailError },
      birthDate: { ...prev.birthDate, error: birthDateError },
    }));

    return !firstNameError && !lastNameError && !emailError && !birthDateError;
  };

  const handleBack = async () => {
    await logout();
    navigation.navigate('Login');
  };

  const handleNext = async () => {
    if (!validateForm()) {
      return;
    }

    try {
      setLoading(true);
      const response = await userAPI.profileSetup({
        first_name: form.firstName.value,
        last_name: form.lastName.value,
        email_id: form.email.value,
        birth_date: new Date(form.birthDate.value).toISOString(),
      });

      if (response.status && response.data) {
        setUser(response.data);
        Toast.show({
          type: 'success',
          text1: 'Profile Updated',
          text2: 'Your profile has been set up successfully',
        });
        navigation.navigate('AddProfilePic');
      } else {
        Toast.show({
          type: 'error',
          text1: 'Profile Setup Failed',
          text2: response.message || 'Something went wrong',
        });
      }
    } catch (error: any) {
      Toast.show({
        type: 'error',
        text1: 'Profile Setup Failed',
        text2: error.message || 'Something went wrong',
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <View style={styles.container}>
      <LinearGradient
        colors={['#1976D2', '#1565C0', '#0D47A1']}
        style={styles.backgroundGradient}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      />

      <Header
        title="Complete Your Profile"
        showBackButton={true}
        onBackPress={handleBack}
      />

      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.content}>
        <Animated.View
          style={[
            styles.header,
            {
              opacity: headerOpacity,
              transform: [{ translateY: headerTranslateY }],
            },
          ]}>
          <Text style={styles.subtitle}>
            Help us personalize your experience by providing some basic
            information
          </Text>
        </Animated.View>

        <Animated.View
          style={[
            styles.formCard,
            {
              opacity: formOpacity,
              transform: [{ translateY: formTranslateY }],
            },
          ]}>
          <View style={styles.form}>
            <Input
              label="First Name"
              placeholder="Enter your first name"
              value={form.firstName.value}
              onChangeText={text => updateField('firstName', text)}
              error={form.firstName.error}
              autoCapitalize="words"
            />

            <Input
              label="Last Name"
              placeholder="Enter your last name"
              value={form.lastName.value}
              onChangeText={text => updateField('lastName', text)}
              error={form.lastName.error}
              autoCapitalize="words"
            />

            <Input
              label="Email Address"
              placeholder="Enter your email address"
              value={form.email.value}
              onChangeText={text => updateField('email', text)}
              error={form.email.error}
              keyboardType="email-address"
              autoCapitalize="none"
            />

            <Input
              label="Date of Birth"
              placeholder="YYYY-MM-DD"
              value={form.birthDate.value}
              onChangeText={text => updateField('birthDate', text)}
              error={form.birthDate.error}
              keyboardType="numeric"
              helperText="Format: YYYY-MM-DD (e.g., 1990-01-15)"
            />

            <RippleButton
              title="Next"
              onPress={handleNext}
              loading={isLoading}
              disabled={isLoading}
              fullWidth
              style={styles.nextButton}
            />
          </View>
        </Animated.View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  backgroundGradient: {
    ...StyleSheet.absoluteFillObject,
  },
  scrollView: {
    flex: 1,
  },
  content: {
    flexGrow: 1,
    paddingHorizontal: Spacing.lg,
    paddingTop: Spacing.lg,
    paddingBottom: Spacing.xl,
  },
  header: {
    marginBottom: Spacing.lg,
    alignItems: 'center',
  },
  subtitle: {
    ...Typography.body1,
    color: Colors.white,
    lineHeight: 24,
    textAlign: 'center',
    fontSize: 16,
    opacity: 0.9,
    textShadowColor: 'rgba(0, 0, 0, 0.2)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 4,
  },
  formCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderRadius: 24,
    padding: Spacing.xl,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 10 },
    shadowOpacity: 0.3,
    shadowRadius: 20,
    elevation: 15,
  },
  form: {
    flex: 1,
  },
  nextButton: {
    marginTop: Spacing.xl,
    borderRadius: 16,
    paddingVertical: Spacing.sm,
    shadowColor: Colors.primary,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
});

export default ProfileSetupScreen;
