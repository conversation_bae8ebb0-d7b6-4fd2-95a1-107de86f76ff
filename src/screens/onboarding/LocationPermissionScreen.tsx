import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>Button } from '@components/common';
import { NavigationProp } from '@react-navigation/native';
import { useAppStore } from '@store/appStore';
import { RootStackParamList } from '@types';
import { requestLocationPermission } from '@utils/permissions';
import { Colors, Spacing } from '@utils/theme';
import { Typography } from '@utils/typography';
import React, { useEffect, useRef } from 'react';
import {
  ScrollView,
  StyleSheet,
  View,
  Animated,
  Dimensions,
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import { Text } from 'react-native-paper';
import Toast from 'react-native-toast-message';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';

type LocationPermissionScreenNavigationProp = NavigationProp<
  RootStackParamList,
  'LocationPermission'
>;

interface Props {
  navigation: LocationPermissionScreenNavigationProp;
}

const { width, height } = Dimensions.get('window');

const LocationPermissionScreen: React.FC<Props> = ({ navigation }) => {
  // Animation values
  const headerOpacity = useRef(new Animated.Value(0)).current;
  const headerTranslateY = useRef(new Animated.Value(-30)).current;
  const iconOpacity = useRef(new Animated.Value(0)).current;
  const iconScale = useRef(new Animated.Value(0.5)).current;
  const buttonOpacity = useRef(new Animated.Value(0)).current;
  const buttonTranslateY = useRef(new Animated.Value(50)).current;

  useEffect(() => {
    // Staggered entrance animations
    Animated.sequence([
      Animated.parallel([
        Animated.timing(headerOpacity, {
          toValue: 1,
          duration: 800,
          useNativeDriver: true,
        }),
        Animated.timing(headerTranslateY, {
          toValue: 0,
          duration: 800,
          useNativeDriver: true,
        }),
      ]),
      Animated.parallel([
        Animated.timing(iconOpacity, {
          toValue: 1,
          duration: 600,
          useNativeDriver: true,
        }),
        Animated.spring(iconScale, {
          toValue: 1,
          tension: 50,
          friction: 7,
          useNativeDriver: true,
        }),
      ]),
      Animated.parallel([
        Animated.timing(buttonOpacity, {
          toValue: 1,
          duration: 600,
          useNativeDriver: true,
        }),
        Animated.timing(buttonTranslateY, {
          toValue: 0,
          duration: 600,
          useNativeDriver: true,
        }),
      ]),
    ]).start();
  }, []);
  const { setLocationPermission } = useAppStore();

  const handleBack = () => {
    navigation.navigate('AddProfilePic');
  };

  const handleAllowLocation = async () => {
    const granted = await requestLocationPermission();

    if (granted) {
      setLocationPermission(true);
      Toast.show({
        type: 'success',
        text1: 'Location Access Granted',
        text2: 'Thank you for enabling location services',
      });
      navigation.navigate('AddHomeAddress');
    } else {
      Toast.show({
        type: 'error',
        text1: 'Location Access Required',
        text2: 'Location access is mandatory for safety features',
      });
    }
  };

  return (
    <View style={styles.container}>
      <LinearGradient
        colors={['#1976D2', '#1565C0', '#0D47A1']}
        style={styles.backgroundGradient}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      />

      <Header
        title="Enable Location Services"
        showBackButton={true}
        onBackPress={handleBack}
      />

      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.content}>
        <Animated.View
          style={[
            styles.header,
            {
              opacity: headerOpacity,
              transform: [{ translateY: headerTranslateY }],
            },
          ]}>
          <Text style={styles.subtitle}>
            Crime Lens needs access to your location to provide safety features
          </Text>
        </Animated.View>

        <Animated.View
          style={[
            styles.iconSection,
            {
              opacity: iconOpacity,
              transform: [{ scale: iconScale }],
            },
          ]}>
          <View style={styles.iconContainer}>
            <Icon name="map-marker" size={80} color={Colors.primary} />
            <View style={styles.iconGlow} />
          </View>
        </Animated.View>

        <View style={styles.featuresCard}>
          <View style={styles.featuresSection}>
            <View style={styles.feature}>
              <Icon name="shield-check" size={24} color={Colors.success} />
              <Text style={styles.featureText}>Real-time safety alerts</Text>
            </View>

            <View style={styles.feature}>
              <Icon name="map-search" size={24} color={Colors.success} />
              <Text style={styles.featureText}>Find nearby safe locations</Text>
            </View>

            <View style={styles.feature}>
              <Icon name="phone-alert" size={24} color={Colors.success} />
              <Text style={styles.featureText}>Quick emergency assistance</Text>
            </View>

            <View style={styles.feature}>
              <Icon name="account-group" size={24} color={Colors.success} />
              <Text style={styles.featureText}>
                Share location with trusted contacts
              </Text>
            </View>
          </View>
        </View>

        <Animated.View
          style={[
            styles.buttonSection,
            {
              opacity: buttonOpacity,
              transform: [{ translateY: buttonTranslateY }],
            },
          ]}>
          <RippleButton
            title="Allow Location Access"
            onPress={handleAllowLocation}
            fullWidth
            style={styles.allowButton}
          />

          <Text style={styles.disclaimer}>
            Your location data is encrypted and only used for safety purposes.
            We never share your location without your consent.
          </Text>
        </Animated.View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  backgroundGradient: {
    ...StyleSheet.absoluteFillObject,
  },
  scrollView: {
    flex: 1,
  },
  content: {
    flexGrow: 1,
    paddingHorizontal: Spacing.lg,
    paddingTop: Spacing.lg,
    paddingBottom: Spacing.xl,
    justifyContent: 'space-between',
  },
  header: {
    marginBottom: Spacing.lg,
    alignItems: 'center',
  },
  subtitle: {
    ...Typography.body1,
    color: Colors.white,
    textAlign: 'center',
    lineHeight: 24,
    fontSize: 16,
    opacity: 0.9,
    textShadowColor: 'rgba(0, 0, 0, 0.2)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 4,
  },
  iconSection: {
    alignItems: 'center',
    marginBottom: Spacing.xl,
  },
  iconContainer: {
    width: 160,
    height: 160,
    borderRadius: 80,
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 10 },
    shadowOpacity: 0.3,
    shadowRadius: 20,
    elevation: 15,
  },
  iconGlow: {
    position: 'absolute',
    width: 140,
    height: 140,
    borderRadius: 70,
    backgroundColor: 'rgba(25, 118, 210, 0.2)',
    shadowColor: Colors.primary,
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0.6,
    shadowRadius: 30,
    elevation: 10,
  },
  featuresCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderRadius: 24,
    padding: Spacing.xl,
    marginBottom: Spacing.xl,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 10 },
    shadowOpacity: 0.3,
    shadowRadius: 20,
    elevation: 15,
  },
  featuresSection: {
    marginBottom: 0,
  },
  feature: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Spacing.md,
    paddingHorizontal: Spacing.sm,
    backgroundColor: 'rgba(25, 118, 210, 0.05)',
    borderRadius: 12,
    paddingVertical: Spacing.sm,
  },
  featureText: {
    ...Typography.body1,
    color: Colors.text.primary,
    marginLeft: Spacing.md,
    flex: 1,
    fontSize: 15,
    fontWeight: '500',
  },
  buttonSection: {
    paddingBottom: Spacing.xl,
    paddingHorizontal: Spacing.md,
  },
  allowButton: {
    marginBottom: Spacing.lg,
    borderRadius: 16,
    paddingVertical: Spacing.sm,
    shadowColor: Colors.primary,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  disclaimer: {
    ...Typography.caption,
    color: Colors.white,
    textAlign: 'center',
    lineHeight: 18,
    fontSize: 12,
    opacity: 0.8,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 12,
    padding: Spacing.md,
    textShadowColor: 'rgba(0, 0, 0, 0.2)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
});

export default LocationPermissionScreen;
