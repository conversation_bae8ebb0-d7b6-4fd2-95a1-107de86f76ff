import { <PERSON><PERSON>, <PERSON>Picker, <PERSON><PERSON><PERSON>Button } from '@components/common';
import { NavigationProp } from '@react-navigation/native';
import { userAPI } from '@services/endpoints';
import { useAuthStore } from '@store/authStore';
import { RootStackParamList } from '@types';
import { Colors, Spacing } from '@utils/theme';
import React, { useState, useEffect, useRef } from 'react';
import {
  ScrollView,
  StyleSheet,
  View,
  Animated,
  Dimensions,
} from 'react-native';
import { Text } from 'react-native-paper';
import Toast from 'react-native-toast-message';
import LinearGradient from 'react-native-linear-gradient';

import { Typography } from '@utils/typography';

type AddProfilePicScreenNavigationProp = NavigationProp<
  RootStackParamList,
  'AddProfilePic'
>;

interface Props {
  navigation: AddProfilePicScreenNavigationProp;
}

const { width, height } = Dimensions.get('window');

const AddProfilePicScreen: React.FC<Props> = ({ navigation }) => {
  const { user, setUser, setLoading, isLoading } = useAuthStore();
  const [selectedImage, setSelectedImage] = useState<string | undefined>(
    user?.profile_picture,
  );

  // Animation values
  const headerOpacity = useRef(new Animated.Value(0)).current;
  const headerTranslateY = useRef(new Animated.Value(-30)).current;
  const imageOpacity = useRef(new Animated.Value(0)).current;
  const imageScale = useRef(new Animated.Value(0.8)).current;
  const buttonOpacity = useRef(new Animated.Value(0)).current;
  const buttonTranslateY = useRef(new Animated.Value(50)).current;

  useEffect(() => {
    // Staggered entrance animations
    Animated.sequence([
      Animated.parallel([
        Animated.timing(headerOpacity, {
          toValue: 1,
          duration: 800,
          useNativeDriver: true,
        }),
        Animated.timing(headerTranslateY, {
          toValue: 0,
          duration: 800,
          useNativeDriver: true,
        }),
      ]),
      Animated.parallel([
        Animated.timing(imageOpacity, {
          toValue: 1,
          duration: 600,
          useNativeDriver: true,
        }),
        Animated.spring(imageScale, {
          toValue: 1,
          tension: 50,
          friction: 7,
          useNativeDriver: true,
        }),
      ]),
      Animated.parallel([
        Animated.timing(buttonOpacity, {
          toValue: 1,
          duration: 600,
          useNativeDriver: true,
        }),
        Animated.timing(buttonTranslateY, {
          toValue: 0,
          duration: 600,
          useNativeDriver: true,
        }),
      ]),
    ]).start();
  }, []);

  const handleImageSelect = (imageUri: string) => {
    setSelectedImage(imageUri);
  };

  const handleNext = async () => {
    if (!selectedImage) {
      Toast.show({
        type: 'error',
        text1: 'Profile Picture Required',
        text2: 'Please select a profile picture to continue',
      });
      return;
    }

    try {
      setLoading(true);

      // Create FormData for file upload
      const formData = new FormData();
      formData.append('file', {
        uri: selectedImage,
        type: 'image/jpeg',
        name: 'profile.jpg',
      } as any);

      const response = await userAPI.uploadProfilePicture(formData);

      if (response.status && response.data) {
        // Update user with new profile picture
        const updatedUser = {
          ...user!,
          profile_picture: response.data.profile_picture,
        };
        setUser(updatedUser);

        Toast.show({
          type: 'success',
          text1: 'Profile Picture Updated',
          text2: 'Your profile picture has been uploaded successfully',
        });

        navigation.navigate('LocationPermission');
      } else {
        Toast.show({
          type: 'error',
          text1: 'Upload Failed',
          text2: response.message || 'Something went wrong',
        });
      }
    } catch (error: any) {
      Toast.show({
        type: 'error',
        text1: 'Upload Failed',
        text2: error.message || 'Something went wrong',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleBack = () => {
    navigation.navigate('ProfileSetup');
  };

  const handleSkip = () => {
    navigation.navigate('LocationPermission');
  };

  return (
    <View style={styles.container}>
      <LinearGradient
        colors={['#1976D2', '#1565C0', '#0D47A1']}
        style={styles.backgroundGradient}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      />

      <Header
        title="Add Profile Picture"
        showBackButton={true}
        onBackPress={handleBack}
      />

      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.content}>
        <Animated.View
          style={[
            styles.header,
            {
              opacity: headerOpacity,
              transform: [{ translateY: headerTranslateY }],
            },
          ]}>
          <Text style={styles.subtitle}>
            Add a profile picture to help others recognize you
          </Text>
        </Animated.View>

        <Animated.View
          style={[
            styles.imageSection,
            {
              opacity: imageOpacity,
              transform: [{ scale: imageScale }],
            },
          ]}>
          <View style={styles.imageCard}>
            <ImagePicker
              imageUri={selectedImage}
              onImageSelect={handleImageSelect}
              size={150}
              placeholder="Tap to add your photo"
            />
          </View>
        </Animated.View>

        <Animated.View
          style={[
            styles.buttonSection,
            {
              opacity: buttonOpacity,
              transform: [{ translateY: buttonTranslateY }],
            },
          ]}>
          <RippleButton
            title="Next"
            onPress={handleNext}
            loading={isLoading}
            disabled={isLoading}
            fullWidth
            style={styles.nextButton}
          />

          <RippleButton
            title="Skip for now"
            onPress={handleSkip}
            variant="text"
            disabled={isLoading}
            style={styles.skipButton}
          />
        </Animated.View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  backgroundGradient: {
    ...StyleSheet.absoluteFillObject,
  },
  scrollView: {
    flex: 1,
  },
  content: {
    flexGrow: 1,
    paddingHorizontal: Spacing.lg,
    paddingTop: Spacing.lg,
    paddingBottom: Spacing.xl,
    justifyContent: 'space-between',
  },
  header: {
    marginBottom: Spacing.lg,
    alignItems: 'center',
  },
  subtitle: {
    ...Typography.body1,
    color: Colors.white,
    textAlign: 'center',
    lineHeight: 24,
    fontSize: 16,
    opacity: 0.9,
    textShadowColor: 'rgba(0, 0, 0, 0.2)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 4,
  },
  imageSection: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: Spacing['2xl'],
  },
  imageCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderRadius: 24,
    padding: Spacing.xl,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 10 },
    shadowOpacity: 0.3,
    shadowRadius: 20,
    elevation: 15,
    alignItems: 'center',
  },
  buttonSection: {
    paddingBottom: Spacing.xl,
    paddingHorizontal: Spacing.md,
  },
  nextButton: {
    marginBottom: Spacing.md,
    borderRadius: 16,
    paddingVertical: Spacing.sm,
    shadowColor: Colors.primary,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  skipButton: {
    alignSelf: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 12,
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.sm,
  },
});

export default AddProfilePicScreen;
