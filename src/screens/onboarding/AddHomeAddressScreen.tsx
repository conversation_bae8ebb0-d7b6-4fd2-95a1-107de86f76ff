import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>Button } from '@components/common';
import Geolocation from '@react-native-community/geolocation';
import { NavigationProp } from '@react-navigation/native';
import { userAPI } from '@services/endpoints';
import { useAppStore } from '@store/appStore';
import { useAuthStore } from '@store/authStore';
import { Location, RootStackParamList } from '@types';
import { Colors, Spacing } from '@utils/theme';
import React, { useEffect, useState, useRef } from 'react';
import {
  Alert,
  ScrollView,
  StyleSheet,
  View,
  Animated,
  Dimensions,
} from 'react-native';
import { LeafletView } from 'react-native-leaflet-view';
import { Text } from 'react-native-paper';
import Toast from 'react-native-toast-message';
import LinearGradient from 'react-native-linear-gradient';

import { Typography } from '@/utils/typography';

type AddHomeAddressScreenNavigationProp = NavigationProp<
  RootStackParamList,
  'AddHomeAddress'
>;

interface Props {
  navigation: AddHomeAddressScreenNavigationProp;
}

const { width, height } = Dimensions.get('window');

const AddHomeAddressScreen: React.FC<Props> = ({ navigation }) => {
  const { user, setUser, setLoading, isLoading } = useAuthStore();
  const { setCurrentLocation } = useAppStore();
  const [address, setAddress] = useState('');
  const [selectedLocation, setSelectedLocation] = useState<Location | null>(
    null,
  );
  const [currentLocation, setCurrentLocationState] = useState<Location | null>(
    null,
  );
  const [isGettingLocation, setIsGettingLocation] = useState(false);

  // Animation values
  const headerOpacity = useRef(new Animated.Value(0)).current;
  const headerTranslateY = useRef(new Animated.Value(-30)).current;
  const mapOpacity = useRef(new Animated.Value(0)).current;
  const mapScale = useRef(new Animated.Value(0.9)).current;
  const buttonOpacity = useRef(new Animated.Value(0)).current;
  const buttonTranslateY = useRef(new Animated.Value(50)).current;

  const handleBack = () => {
    navigation.navigate('LocationPermission');
  };

  useEffect(() => {
    getCurrentLocation();

    // Staggered entrance animations
    Animated.sequence([
      Animated.parallel([
        Animated.timing(headerOpacity, {
          toValue: 1,
          duration: 800,
          useNativeDriver: true,
        }),
        Animated.timing(headerTranslateY, {
          toValue: 0,
          duration: 800,
          useNativeDriver: true,
        }),
      ]),
      Animated.parallel([
        Animated.timing(mapOpacity, {
          toValue: 1,
          duration: 600,
          useNativeDriver: true,
        }),
        Animated.spring(mapScale, {
          toValue: 1,
          tension: 50,
          friction: 7,
          useNativeDriver: true,
        }),
      ]),
      Animated.parallel([
        Animated.timing(buttonOpacity, {
          toValue: 1,
          duration: 600,
          useNativeDriver: true,
        }),
        Animated.timing(buttonTranslateY, {
          toValue: 0,
          duration: 600,
          useNativeDriver: true,
        }),
      ]),
    ]).start();
  }, []);

  const getCurrentLocation = () => {
    setIsGettingLocation(true);

    Geolocation.getCurrentPosition(
      position => {
        const { latitude, longitude } = position.coords;
        const currentLoc: Location = {
          latitude,
          longitude,
        };
        setCurrentLocationState(currentLoc);
        setCurrentLocation(currentLoc);
        setIsGettingLocation(false);
      },
      error => {
        console.error('Error getting location:', error);
        setIsGettingLocation(false);
        Alert.alert(
          'Location Error',
          'Unable to get your current location. Please search for your address manually.',
        );
      },
      { enableHighAccuracy: true, timeout: 15000, maximumAge: 10000 },
    );
  };

  const handlePlaceSelect = (location: Location) => {
    setSelectedLocation(location);
    setAddress(location.address || '');
  };

  const handleNext = async () => {
    if (!selectedLocation || !address) {
      Toast.show({
        type: 'error',
        text1: 'Address Required',
        text2: 'Please select your home address to continue',
      });
      return;
    }

    try {
      setLoading(true);
      const response = await userAPI.updateAddress({
        home_address: address,
        latitude: selectedLocation.latitude,
        longitude: selectedLocation.longitude,
      });

      if (response.status && response.data) {
        setUser(response.data);
        Toast.show({
          type: 'success',
          text1: 'Address Updated',
          text2: 'Your home address has been saved successfully',
        });
        navigation.replace('MainTabs');
      } else {
        Toast.show({
          type: 'error',
          text1: 'Address Update Failed',
          text2: response.message || 'Something went wrong',
        });
      }
    } catch (error: any) {
      Toast.show({
        type: 'error',
        text1: 'Address Update Failed',
        text2: error.message || 'Something went wrong',
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <View style={styles.container}>
      <LinearGradient
        colors={['#1976D2', '#1565C0', '#0D47A1']}
        style={styles.backgroundGradient}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      />

      <Header
        title="Add Home Address"
        showBackButton={true}
        onBackPress={handleBack}
      />

      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.content}>
        <Animated.View
          style={[
            styles.header,
            {
              opacity: headerOpacity,
              transform: [{ translateY: headerTranslateY }],
            },
          ]}>
          <Text style={styles.subtitle}>
            Help us provide better safety recommendations by adding your home
            address
          </Text>
        </Animated.View>

        <Animated.View
          style={[
            styles.mapSection,
            {
              opacity: mapOpacity,
              transform: [{ scale: mapScale }],
            },
          ]}>
          <View style={styles.mapCard}>
            <LeafletView
              mapCenterPosition={{
                lat:
                  selectedLocation?.latitude ||
                  currentLocation?.latitude ||
                  43.6532,
                lng:
                  selectedLocation?.longitude ||
                  currentLocation?.longitude ||
                  -79.3832,
              }}
              zoom={15}
              mapMarkers={
                selectedLocation
                  ? [
                      {
                        position: {
                          lat: selectedLocation.latitude,
                          lng: selectedLocation.longitude,
                        },
                        icon: '📍',
                        size: [32, 32],
                      },
                    ]
                  : []
              }
              onMapClicked={location => {
                const newLocation: Location = {
                  latitude: location.lat,
                  longitude: location.lng,
                  address: `${location.lat.toFixed(6)}, ${location.lng.toFixed(
                    6,
                  )}`,
                };
                setSelectedLocation(newLocation);
                setAddress(newLocation.address || '');
              }}
              style={styles.map}
            />
          </View>
        </Animated.View>

        <View style={styles.searchCard}>
          <View style={styles.searchSection}>
            <GoogleAutocomplete
              label="Search Address"
              placeholder="Search for your home address..."
              value={address}
              onChangeText={setAddress}
              onPlaceSelect={handlePlaceSelect}
            />

            {selectedLocation && (
              <View style={styles.selectedLocationInfo}>
                <Text style={styles.selectedLocationText}>📍 {address}</Text>
                <Text style={styles.coordinatesText}>
                  Lat: {selectedLocation.latitude.toFixed(6)}, Lng:{' '}
                  {selectedLocation.longitude.toFixed(6)}
                </Text>
              </View>
            )}
          </View>
        </View>

        <Animated.View
          style={[
            styles.buttonSection,
            {
              opacity: buttonOpacity,
              transform: [{ translateY: buttonTranslateY }],
            },
          ]}>
          <RippleButton
            title="Save Address"
            onPress={handleNext}
            loading={isLoading}
            disabled={isLoading || !selectedLocation}
            fullWidth
            style={styles.saveButton}
          />
        </Animated.View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  backgroundGradient: {
    ...StyleSheet.absoluteFillObject,
  },
  scrollView: {
    flex: 1,
  },
  content: {
    flexGrow: 1,
    paddingHorizontal: Spacing.lg,
    paddingTop: Spacing.lg,
    paddingBottom: Spacing.xl,
  },
  header: {
    marginBottom: Spacing.lg,
    alignItems: 'center',
  },
  subtitle: {
    ...Typography.body1,
    color: Colors.white,
    lineHeight: 24,
    textAlign: 'center',
    fontSize: 16,
    opacity: 0.9,
    textShadowColor: 'rgba(0, 0, 0, 0.2)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 4,
  },
  mapSection: {
    marginBottom: Spacing.xl,
    flex: 1,
  },
  mapCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderRadius: 24,
    padding: Spacing.md,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 10 },
    shadowOpacity: 0.3,
    shadowRadius: 20,
    elevation: 15,
  },
  map: {
    height: 250,
    borderRadius: 16,
    overflow: 'hidden',
  },
  searchCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderRadius: 24,
    padding: Spacing.xl,
    marginBottom: Spacing.xl,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 10 },
    shadowOpacity: 0.3,
    shadowRadius: 20,
    elevation: 15,
  },
  searchSection: {
    marginBottom: 0,
  },
  selectedLocationInfo: {
    marginTop: Spacing.md,
    padding: Spacing.md,
    backgroundColor: 'rgba(25, 118, 210, 0.05)',
    borderRadius: 12,
    borderLeftWidth: 4,
    borderLeftColor: Colors.primary,
  },
  selectedLocationText: {
    ...Typography.body1,
    color: Colors.text.primary,
    marginBottom: Spacing.xs,
    fontSize: 15,
    fontWeight: '600',
  },
  coordinatesText: {
    ...Typography.caption,
    color: Colors.text.secondary,
    fontSize: 12,
    fontFamily: 'monospace',
  },
  buttonSection: {
    paddingBottom: Spacing.xl,
    paddingHorizontal: Spacing.md,
  },
  saveButton: {
    marginTop: Spacing.lg,
    borderRadius: 16,
    paddingVertical: Spacing.sm,
    shadowColor: Colors.primary,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
});

export default AddHomeAddressScreen;
