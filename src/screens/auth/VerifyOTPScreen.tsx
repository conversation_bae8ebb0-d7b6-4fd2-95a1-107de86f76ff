import { OTPInput, RippleButton } from '@components/common';
import { NavigationProp, RouteProp } from '@react-navigation/native';
import { authAPI } from '@services/endpoints';
import { useAuthStore } from '@store/authStore';
import { Colors, Spacing } from '@utils/theme';
import { Typography } from '@utils/typography';
import { ValidationRules, validateField } from '@utils/validation';
import React, { useEffect, useState, useRef } from 'react';
import {
  ScrollView,
  StyleSheet,
  View,
  Animated,
  Dimensions,
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import { Text } from 'react-native-paper';
import Toast from 'react-native-toast-message';

import { RootStackParamList } from '../../types';

type VerifyOTPScreenNavigationProp = NavigationProp<
  RootStackParamList,
  'VerifyOTP'
>;
type VerifyOTPScreenRouteProp = RouteProp<RootStackParamList, 'VerifyOTP'>;

interface Props {
  navigation: VerifyOTPScreenNavigationProp;
  route: VerifyOTPScreenRouteProp;
}

const { width, height } = Dimensions.get('window');

const VerifyOTPScreen: React.FC<Props> = ({ navigation, route }) => {
  const { mobile } = route.params;
  const { setToken, setUser, setLoading, isLoading } = useAuthStore();
  const [otp, setOtp] = useState('');
  const [otpError, setOtpError] = useState<string | undefined>();
  const [resendTimer, setResendTimer] = useState(90);
  const [canResend, setCanResend] = useState(false);

  // Animation values
  const headerOpacity = useRef(new Animated.Value(0)).current;
  const headerTranslateY = useRef(new Animated.Value(-30)).current;
  const otpOpacity = useRef(new Animated.Value(0)).current;
  const otpScale = useRef(new Animated.Value(0.9)).current;
  const buttonOpacity = useRef(new Animated.Value(0)).current;
  const buttonTranslateY = useRef(new Animated.Value(50)).current;

  useEffect(() => {
    // Start entrance animations
    Animated.sequence([
      Animated.parallel([
        Animated.timing(headerOpacity, {
          toValue: 1,
          duration: 800,
          useNativeDriver: true,
        }),
        Animated.timing(headerTranslateY, {
          toValue: 0,
          duration: 800,
          useNativeDriver: true,
        }),
      ]),
      Animated.parallel([
        Animated.timing(otpOpacity, {
          toValue: 1,
          duration: 600,
          useNativeDriver: true,
        }),
        Animated.spring(otpScale, {
          toValue: 1,
          tension: 50,
          friction: 7,
          useNativeDriver: true,
        }),
      ]),
      Animated.parallel([
        Animated.timing(buttonOpacity, {
          toValue: 1,
          duration: 600,
          useNativeDriver: true,
        }),
        Animated.timing(buttonTranslateY, {
          toValue: 0,
          duration: 600,
          useNativeDriver: true,
        }),
      ]),
    ]).start();
  }, []);

  useEffect(() => {
    const timer = setInterval(() => {
      setResendTimer(prev => {
        if (prev <= 1) {
          setCanResend(true);
          clearInterval(timer);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const validateOTP = (): boolean => {
    const error = validateField(otp, [
      ValidationRules.required,
      ValidationRules.otp,
    ]);
    setOtpError(error || undefined);
    return !error;
  };

  const handleVerifyOTP = async () => {
    if (!validateOTP()) {
      return;
    }

    try {
      setLoading(true);
      const response = await authAPI.verifyOTP({ mobile, otp });
      if (response.status && response.data.access_token) {
        await setToken(response.data.access_token);
        if (response.data.user) {
          setUser(response.data.user);
        }

        Toast.show({
          type: 'success',
          text1: 'Verification Successful',
          text2: 'Welcome to Crime Lens!',
        });

        // Navigate based on user profile completion
        if (!response.data.user?.first_name) {
          navigation.replace('ProfileSetup');
        } else if (!response.data.user?.profile_picture) {
          navigation.replace('AddProfilePic');
        } else if (!response.data.user?.home_address) {
          navigation.replace('AddHomeAddress');
        } else {
          navigation.replace('MainTabs');
        }
      } else {
        Toast.show({
          type: 'error',
          text1: 'Verification Failed',
          text2: response.message || 'Invalid OTP',
        });
      }
    } catch (error: any) {
      Toast.show({
        type: 'error',
        text1: 'Verification Failed',
        text2: error.message || 'Something went wrong',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleResendOTP = async () => {
    try {
      setLoading(true);
      const response = await authAPI.resendOTP({ mobile });

      if (response.status) {
        Toast.show({
          type: 'success',
          text1: 'OTP Resent',
          text2: 'Please check your mobile for the new verification code',
        });

        setResendTimer(90);
        setCanResend(false);

        const timer = setInterval(() => {
          setResendTimer(prev => {
            if (prev <= 1) {
              setCanResend(true);
              clearInterval(timer);
              return 0;
            }
            return prev - 1;
          });
        }, 1000);
      } else {
        Toast.show({
          type: 'error',
          text1: 'Resend Failed',
          text2: response.message || 'Something went wrong',
        });
      }
    } catch (error: any) {
      Toast.show({
        type: 'error',
        text1: 'Resend Failed',
        text2: error.message || 'Something went wrong',
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <View style={styles.container}>
      <LinearGradient
        colors={['#1976D2', '#1565C0', '#0D47A1']}
        style={styles.backgroundGradient}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      />

      <ScrollView
        style={styles.scrollContainer}
        contentContainerStyle={styles.content}
        showsVerticalScrollIndicator={false}>
        <Animated.View
          style={[
            styles.header,
            {
              opacity: headerOpacity,
              transform: [{ translateY: headerTranslateY }],
            },
          ]}>
          <Text style={styles.title}>Verify Your Number</Text>
          <Text style={styles.subtitle}>
            We've sent a 6-digit verification code to
          </Text>
          <Text style={styles.mobileNumber}>{mobile}</Text>
        </Animated.View>

        <Animated.View
          style={[
            styles.formCard,
            {
              opacity: otpOpacity,
              transform: [{ scale: otpScale }],
            },
          ]}>
          <View style={styles.form}>
            <View style={styles.otpSection}>
              <OTPInput
                value={otp}
                onCodeChanged={setOtp}
                onCodeFilled={setOtp}
                onSubmit={handleVerifyOTP}
                numberOfDigits={6}
                error={otpError}
              />
            </View>

            <View style={styles.resendContainer}>
              {canResend ? (
                <RippleButton
                  title="Resend Code"
                  variant="text"
                  onPress={handleResendOTP}
                  disabled={isLoading}
                  style={styles.resendButton}
                />
              ) : (
                <Text style={styles.timerText}>
                  Resend code in {formatTime(resendTimer)}
                </Text>
              )}
            </View>

            <Animated.View
              style={{
                opacity: buttonOpacity,
                transform: [{ translateY: buttonTranslateY }],
              }}>
              <RippleButton
                title="Verify"
                onPress={handleVerifyOTP}
                loading={isLoading}
                disabled={isLoading || otp.length !== 6}
                fullWidth
                style={styles.verifyButton}
              />
            </Animated.View>
          </View>
        </Animated.View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  backgroundGradient: {
    ...StyleSheet.absoluteFillObject,
  },
  scrollContainer: {
    flex: 1,
  },
  content: {
    flexGrow: 1,
    paddingHorizontal: Spacing.lg,
    paddingTop: height * 0.1,
    paddingBottom: Spacing.xl,
  },
  header: {
    marginBottom: Spacing.xl,
    alignItems: 'center',
  },
  title: {
    ...Typography.h1,
    color: Colors.white,
    marginBottom: Spacing.sm,
    textAlign: 'center',
    fontSize: 32,
    fontWeight: '700',
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 0, height: 2 },
    textShadowRadius: 8,
  },
  subtitle: {
    ...Typography.body1,
    color: Colors.white,
    textAlign: 'center',
    marginBottom: Spacing.xs,
    opacity: 0.9,
    fontSize: 16,
    textShadowColor: 'rgba(0, 0, 0, 0.2)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 4,
  },
  mobileNumber: {
    ...Typography.h6,
    color: Colors.white,
    fontWeight: '700',
    textAlign: 'center',
    fontSize: 18,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.xs,
    borderRadius: 12,
    overflow: 'hidden',
  },
  formCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderRadius: 24,
    padding: Spacing.xl,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 10 },
    shadowOpacity: 0.3,
    shadowRadius: 20,
    elevation: 15,
  },
  form: {
    flex: 1,
  },
  otpSection: {
    marginBottom: Spacing.xl,
    alignItems: 'center',
  },
  resendContainer: {
    alignItems: 'center',
    marginVertical: Spacing.lg,
    backgroundColor: 'rgba(25, 118, 210, 0.05)',
    borderRadius: 12,
    padding: Spacing.md,
  },
  resendButton: {
    backgroundColor: 'transparent',
  },
  timerText: {
    ...Typography.body2,
    color: Colors.text.secondary,
    fontSize: 14,
    fontWeight: '500',
  },
  verifyButton: {
    marginTop: Spacing.lg,
    borderRadius: 16,
    paddingVertical: Spacing.sm,
    shadowColor: Colors.primary,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
});

export default VerifyOTPScreen;
