import { Input } from '@components/common/Input';
import { NavigationProp } from '@react-navigation/native';
import { authAPI } from '@services/endpoints';
import { useAuthStore } from '@store/authStore';
import { LoginForm, RootStackParamList } from '@types';
import { Colors, Spacing } from '@utils/theme';
import { ValidationRules, validateField } from '@utils/validation';
import React, { useState, useEffect, useRef } from 'react';
import {
  Linking,
  ScrollView,
  StyleSheet,
  View,
  Animated,
  Dimensions,
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import { Checkbox, Text } from 'react-native-paper';
import Toast from 'react-native-toast-message';

import { RippleButton } from '@/components/common';
import { Typography } from '@/utils/typography';

type LoginScreenNavigationProp = NavigationProp<RootStackParamList, 'Login'>;

interface Props {
  navigation: LoginScreenNavigationProp;
}

const CANADA_CODE = { code: '1', country: 'Canada', flag: '🇨🇦' };
const { width, height } = Dimensions.get('window');

const LoginScreen: React.FC<Props> = ({ navigation }) => {
  const { setToken, setLoading, isLoading } = useAuthStore();
  const [form, setForm] = useState<LoginForm>({
    countryCode: { value: '1', error: undefined, touched: false },
    mobile: { value: '', error: undefined, touched: false },
    termsAccepted: false,
  });

  // Animation values
  const headerOpacity = useRef(new Animated.Value(0)).current;
  const headerTranslateY = useRef(new Animated.Value(-30)).current;
  const formOpacity = useRef(new Animated.Value(0)).current;
  const formTranslateY = useRef(new Animated.Value(50)).current;

  useEffect(() => {
    // Staggered entrance animations
    Animated.sequence([
      Animated.parallel([
        Animated.timing(headerOpacity, {
          toValue: 1,
          duration: 800,
          useNativeDriver: true,
        }),
        Animated.timing(headerTranslateY, {
          toValue: 0,
          duration: 800,
          useNativeDriver: true,
        }),
      ]),
      Animated.parallel([
        Animated.timing(formOpacity, {
          toValue: 1,
          duration: 600,
          useNativeDriver: true,
        }),
        Animated.timing(formTranslateY, {
          toValue: 0,
          duration: 600,
          useNativeDriver: true,
        }),
      ]),
    ]).start();
  }, []);

  const updateField = (field: keyof LoginForm, value: string) => {
    setForm(prev => ({
      ...prev,
      [field]: {
        ...prev[field],
        value,
        touched: true,
        error: undefined,
      },
    }));
  };

  const validateForm = (): boolean => {
    const mobileError = validateField(form.mobile.value, [
      ValidationRules.required,
      ValidationRules.mobile,
    ]);

    setForm(prev => ({
      ...prev,
      mobile: { ...prev.mobile, error: mobileError },
    }));

    if (!form.termsAccepted) {
      Toast.show({
        type: 'error',
        text1: 'Terms & Conditions',
        text2: 'Please accept the terms and conditions to continue',
      });
      return false;
    }

    return !mobileError;
  };

  const handleLogin = async () => {
    if (!validateForm()) {
      return;
    }

    try {
      setLoading(true);
      const mobile = `${CANADA_CODE.code}${form.mobile.value}`;
      const response = await authAPI.login({ mobile });

      if (response.status) {
        Toast.show({
          type: 'success',
          text1: 'OTP Sent',
          text2: 'Please check your mobile for the verification code',
        });
        navigation.navigate('VerifyOTP', { mobile });
      } else {
        Toast.show({
          type: 'error',
          text1: 'Login Failed',
          text2: response.message || 'Something went wrong',
        });
      }
    } catch (error: any) {
      Toast.show({
        type: 'error',
        text1: 'Login Failed',
        text2: error.message || 'Something went wrong',
      });
    } finally {
      setLoading(false);
    }
  };

  const openTermsOfService = () => {
    // Replace with actual terms URL
    Linking.openURL('https://crimelens.com/terms');
  };

  const openPrivacyPolicy = () => {
    // Replace with actual privacy policy URL
    Linking.openURL('https://crimelens.com/privacy');
  };

  return (
    <View style={styles.container}>
      <LinearGradient
        colors={['#1976D2', '#1565C0', '#0D47A1']}
        style={styles.backgroundGradient}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      />

      <ScrollView
        style={styles.scrollContainer}
        contentContainerStyle={styles.content}
        showsVerticalScrollIndicator={false}>
        <Animated.View
          style={[
            styles.header,
            {
              opacity: headerOpacity,
              transform: [{ translateY: headerTranslateY }],
            },
          ]}>
          <Text style={styles.title}>Welcome Back</Text>
          <Text style={styles.subtitle}>
            Enter your mobile number to continue
          </Text>
        </Animated.View>

        <Animated.View
          style={[
            styles.formCard,
            {
              opacity: formOpacity,
              transform: [{ translateY: formTranslateY }],
            },
          ]}>
          <View style={styles.form}>
            <View style={styles.phoneInputContainer}>
              <View style={styles.countryCodeContainer}>
                <Text style={styles.countryCode}>
                  {CANADA_CODE.flag} {CANADA_CODE.code}
                </Text>
              </View>
              <View style={styles.mobileInputContainer}>
                <Input
                  label="Mobile Number"
                  placeholder="Enter your mobile number"
                  value={form.mobile.value}
                  onChangeText={text => updateField('mobile', text)}
                  error={form.mobile.error}
                  keyboardType="phone-pad"
                  maxLength={10}
                />
              </View>
            </View>

            <View style={styles.checkboxContainer}>
              <Checkbox
                status={form.termsAccepted ? 'checked' : 'unchecked'}
                onPress={() =>
                  setForm(prev => ({
                    ...prev,
                    termsAccepted: !prev.termsAccepted,
                  }))
                }
                color={Colors.primary}
              />
              <View style={styles.checkboxText}>
                <Text style={styles.termsText}>I agree to the </Text>
                <Text style={styles.linkText} onPress={openTermsOfService}>
                  Terms of Service
                </Text>
                <Text style={styles.termsText}> and </Text>
                <Text style={styles.linkText} onPress={openPrivacyPolicy}>
                  Privacy Policy
                </Text>
              </View>
            </View>

            <RippleButton
              title="Send OTP"
              variant="contained"
              loading={isLoading}
              disabled={isLoading}
              onPress={handleLogin}
              style={styles.loginButton}
            />
          </View>
        </Animated.View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  backgroundGradient: {
    ...StyleSheet.absoluteFillObject,
  },
  scrollContainer: {
    flex: 1,
  },
  content: {
    flexGrow: 1,
    paddingHorizontal: Spacing.lg,
    paddingTop: height * 0.1,
    paddingBottom: Spacing.xl,
  },
  header: {
    marginBottom: Spacing.xl,
    alignItems: 'center',
  },
  title: {
    ...Typography.h1,
    color: Colors.white,
    marginBottom: Spacing.sm,
    textAlign: 'center',
    fontSize: 36,
    fontWeight: '700',
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 0, height: 2 },
    textShadowRadius: 8,
  },
  subtitle: {
    ...Typography.body1,
    color: Colors.white,
    textAlign: 'center',
    lineHeight: 24,
    opacity: 0.9,
    fontSize: 16,
    textShadowColor: 'rgba(0, 0, 0, 0.2)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 4,
  },
  formCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderRadius: 24,
    padding: Spacing.xl,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 10 },
    shadowOpacity: 0.3,
    shadowRadius: 20,
    elevation: 15,
    backdropFilter: 'blur(10px)',
  },
  form: {
    flex: 1,
  },
  phoneInputContainer: {
    flexDirection: 'row',
    marginBottom: Spacing.lg,
  },
  countryCodeContainer: {
    width: 100,
    marginRight: Spacing.sm,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: Colors.surface,
    borderRadius: 12,
    paddingVertical: Spacing.md,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  countryCode: {
    ...Typography.body1,
    color: Colors.text.primary,
    fontWeight: '600',
  },
  mobileInputContainer: {
    flex: 1,
  },
  checkboxContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: Spacing.xl,
    backgroundColor: 'rgba(25, 118, 210, 0.05)',
    borderRadius: 12,
    padding: Spacing.md,
  },
  checkboxText: {
    flex: 1,
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginLeft: Spacing.sm,
    marginTop: Spacing.xs,
  },
  termsText: {
    ...Typography.body2,
    color: Colors.text.secondary,
    fontSize: 14,
  },
  linkText: {
    ...Typography.body2,
    color: Colors.primary,
    textDecorationLine: 'underline',
    fontWeight: '600',
    fontSize: 14,
  },
  loginButton: {
    marginTop: Spacing.lg,
    borderRadius: 16,
    paddingVertical: Spacing.sm,
    shadowColor: Colors.primary,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
});

export default LoginScreen;
