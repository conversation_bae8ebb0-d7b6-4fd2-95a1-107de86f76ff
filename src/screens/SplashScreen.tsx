import { AppLogo } from '@assets/images';
import { NavigationProp } from '@react-navigation/native';
import { useAppStore } from '@store/appStore';
import { useAuthStore } from '@store/authStore';
import { RootStackParamList } from '@types';
import { Colors, Spacing } from '@utils/theme';
import { Typography } from '@utils/typography';
import React, { useEffect, useRef } from 'react';
import { StyleSheet, View, Image, Animated, Dimensions } from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import { Text } from 'react-native-paper';

type SplashScreenNavigationProp = NavigationProp<RootStackParamList, 'Splash'>;

interface Props {
  navigation: SplashScreenNavigationProp;
}

const { width, height } = Dimensions.get('window');

const SplashScreen: React.FC<Props> = ({ navigation }) => {
  const { setFirstLaunch } = useAppStore();
  const { isAuthenticated, user, initializeAuth } = useAuthStore();

  // Animation values
  const logoScale = useRef(new Animated.Value(0)).current;
  const logoOpacity = useRef(new Animated.Value(0)).current;
  const textOpacity = useRef(new Animated.Value(0)).current;
  const backgroundOpacity = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    const initializeApp = async () => {
      // Initialize auth first
      await initializeAuth();

      // Start animations
      Animated.sequence([
        // Background fade in
        Animated.timing(backgroundOpacity, {
          toValue: 1,
          duration: 500,
          useNativeDriver: true,
        }),
        // Logo scale and fade in
        Animated.parallel([
          Animated.spring(logoScale, {
            toValue: 1,
            tension: 50,
            friction: 7,
            useNativeDriver: true,
          }),
          Animated.timing(logoOpacity, {
            toValue: 1,
            duration: 800,
            useNativeDriver: true,
          }),
        ]),
        // Text fade in
        Animated.timing(textOpacity, {
          toValue: 1,
          duration: 600,
          useNativeDriver: true,
        }),
      ]).start();

      const timer = setTimeout(() => {
        setFirstLaunch(false);
        navigation.replace('MainTabs');
        // if (!isAuthenticated) {
        //   navigation.replace('GetStarted');
        // } else if (!user?.first_name) {
        //   navigation.replace('ProfileSetup');
        // } else if (!user?.profile_picture) {
        //   navigation.replace('AddProfilePic');
        // } else if (!user?.home_address) {
        //   navigation.replace('AddHomeAddress');
        // } else {
        //   navigation.replace('MainTabs');
        // }
      }, 3000);

      return () => clearTimeout(timer);
    };

    initializeApp();
  }, [
    navigation,
    setFirstLaunch,
    isAuthenticated,
    user,
    initializeAuth,
    logoScale,
    logoOpacity,
    textOpacity,
    backgroundOpacity,
  ]);

  return (
    <View style={styles.container}>
      <Animated.View
        style={[styles.backgroundGradient, { opacity: backgroundOpacity }]}>
        <LinearGradient
          colors={['#1976D2', '#1565C0', '#0D47A1']}
          style={styles.gradient}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
        />
      </Animated.View>

      <Animated.View style={[styles.logoContainer, { opacity: logoOpacity }]}>
        <Animated.View
          style={[
            styles.logoPlaceholder,
            {
              transform: [{ scale: logoScale }],
              opacity: logoOpacity,
            },
          ]}>
          <Image source={AppLogo} style={styles.logoImage} />
          <View style={styles.logoGlow} />
        </Animated.View>

        <Animated.View style={{ opacity: textOpacity }}>
          <Text style={styles.appName}>Crime Lens</Text>
          <Text style={styles.tagline}>Your Safety Companion</Text>
        </Animated.View>
      </Animated.View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  backgroundGradient: {
    ...StyleSheet.absoluteFillObject,
  },
  gradient: {
    flex: 1,
  },
  logoContainer: {
    alignItems: 'center',
    zIndex: 1,
  },
  logoPlaceholder: {
    width: 160,
    height: 160,
    borderRadius: 80,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: Spacing.xl,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.3,
    shadowRadius: 20,
    elevation: 15,
  },
  logoImage: {
    width: 120,
    height: 120,
    borderRadius: 60,
    zIndex: 2,
  },
  logoGlow: {
    position: 'absolute',
    width: 140,
    height: 140,
    borderRadius: 70,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    shadowColor: '#fff',
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0.5,
    shadowRadius: 30,
    elevation: 10,
  },
  appName: {
    ...Typography.h1,
    color: Colors.white,
    marginBottom: Spacing.sm,
    textAlign: 'center',
    fontSize: 48,
    fontWeight: '700',
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 0, height: 2 },
    textShadowRadius: 8,
  },
  tagline: {
    ...Typography.h6,
    color: Colors.white,
    opacity: 0.95,
    textAlign: 'center',
    fontSize: 18,
    fontWeight: '400',
    textShadowColor: 'rgba(0, 0, 0, 0.2)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 4,
  },
});

export default SplashScreen;
