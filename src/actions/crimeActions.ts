import { Alert } from 'react-native';
import { crimeAPI } from '@services/endpoints';
import { 
  CrimeSearchParams, 
  CrimeSearchResponse, 
  FilterData,
  CrimeRecord 
} from '@/types';

/**
 * Convert FilterData from the UI to CrimeSearchParams for the API
 */
export const convertFiltersToCrimeParams = (filters: FilterData): CrimeSearchParams => {
  const params: CrimeSearchParams = {};

  // Convert dates to ISO format if provided
  if (filters.fromDate) {
    params.from_date = `${filters.fromDate}T00:00:00`;
  }
  
  if (filters.toDate) {
    params.to_date = `${filters.toDate}T23:59:59`;
  }

  // Add crime type
  if (filters.crimeType) {
    params.crime_type = filters.crimeType;
  }

  // Add location data
  if (filters.city) {
    params.city = filters.city;
  }

  if (filters.area) {
    params.area = filters.area;
  }

  // Set default pagination
  params.page = 1;
  params.limit = 20;

  return params;
};

/**
 * Search crimes based on filter data
 */
export const searchCrimes = async (
  filters: FilterData,
  options?: {
    page?: number;
    limit?: number;
    severity?: 'low' | 'medium' | 'high';
    status?: 'reported' | 'investigating' | 'resolved' | 'closed';
  }
): Promise<CrimeSearchResponse | null> => {
  try {
    // Convert UI filters to API parameters
    const searchParams = convertFiltersToCrimeParams(filters);

    // Add optional parameters
    if (options?.page) searchParams.page = options.page;
    if (options?.limit) searchParams.limit = options.limit;
    if (options?.severity) searchParams.severity = options.severity;
    if (options?.status) searchParams.status = options.status;

    console.log('🔍 Searching crimes with params:', searchParams);

    // Call the API
    const response = await crimeAPI.searchCrimes(searchParams);

    if (response.success && response.data) {
      console.log('✅ Crime search successful:', {
        totalRecords: response.data.pagination.total_records,
        currentPage: response.data.pagination.current_page,
        recordsReturned: response.data.data.length,
      });
      
      return response.data;
    } else {
      console.error('❌ Crime search failed:', response.message);
      Alert.alert('Search Failed', response.message || 'Unable to fetch crime data');
      return null;
    }
  } catch (error: any) {
    console.error('❌ Crime search error:', error);
    Alert.alert(
      'Search Error', 
      error.message || 'An error occurred while searching for crime data'
    );
    return null;
  }
};

/**
 * Get basic crime data without filters (for initial load)
 */
export const getBasicCrimeData = async (
  limit: number = 10
): Promise<CrimeSearchResponse | null> => {
  try {
    const params: CrimeSearchParams = {
      page: 1,
      limit,
    };

    console.log('🔍 Getting basic crime data with limit:', limit);

    const response = await crimeAPI.searchCrimes(params);

    if (response.success && response.data) {
      console.log('✅ Basic crime data loaded:', {
        totalRecords: response.data.pagination.total_records,
        recordsReturned: response.data.data.length,
      });
      
      return response.data;
    } else {
      console.error('❌ Basic crime data fetch failed:', response.message);
      return null;
    }
  } catch (error: any) {
    console.error('❌ Basic crime data error:', error);
    return null;
  }
};

/**
 * Search crimes by specific crime type
 */
export const searchCrimesByType = async (
  crimeType: string,
  limit: number = 20
): Promise<CrimeSearchResponse | null> => {
  try {
    const params: CrimeSearchParams = {
      crime_type: crimeType,
      page: 1,
      limit,
    };

    console.log('🔍 Searching crimes by type:', crimeType);

    const response = await crimeAPI.searchCrimes(params);

    if (response.success && response.data) {
      return response.data;
    } else {
      Alert.alert('Search Failed', `Unable to fetch ${crimeType} crime data`);
      return null;
    }
  } catch (error: any) {
    console.error('❌ Crime type search error:', error);
    Alert.alert('Search Error', 'An error occurred while searching for crime data');
    return null;
  }
};

/**
 * Search crimes by location
 */
export const searchCrimesByLocation = async (
  city: string,
  area?: string,
  limit: number = 20
): Promise<CrimeSearchResponse | null> => {
  try {
    const params: CrimeSearchParams = {
      city,
      page: 1,
      limit,
    };

    if (area) {
      params.area = area;
    }

    console.log('🔍 Searching crimes by location:', { city, area });

    const response = await crimeAPI.searchCrimes(params);

    if (response.success && response.data) {
      return response.data;
    } else {
      Alert.alert('Search Failed', `Unable to fetch crime data for ${city}`);
      return null;
    }
  } catch (error: any) {
    console.error('❌ Location search error:', error);
    Alert.alert('Search Error', 'An error occurred while searching for crime data');
    return null;
  }
};

/**
 * Search crimes by date range
 */
export const searchCrimesByDateRange = async (
  fromDate: string,
  toDate: string,
  limit: number = 20
): Promise<CrimeSearchResponse | null> => {
  try {
    const params: CrimeSearchParams = {
      from_date: `${fromDate}T00:00:00`,
      to_date: `${toDate}T23:59:59`,
      page: 1,
      limit,
    };

    console.log('🔍 Searching crimes by date range:', { fromDate, toDate });

    const response = await crimeAPI.searchCrimes(params);

    if (response.success && response.data) {
      return response.data;
    } else {
      Alert.alert('Search Failed', 'Unable to fetch crime data for the selected date range');
      return null;
    }
  } catch (error: any) {
    console.error('❌ Date range search error:', error);
    Alert.alert('Search Error', 'An error occurred while searching for crime data');
    return null;
  }
};

/**
 * Get crime details by ID
 */
export const getCrimeDetails = async (crimeId: string): Promise<CrimeRecord | null> => {
  try {
    console.log('🔍 Getting crime details for ID:', crimeId);

    const response = await crimeAPI.getCrimeById(crimeId);

    if (response.success && response.data) {
      return response.data;
    } else {
      Alert.alert('Error', 'Unable to fetch crime details');
      return null;
    }
  } catch (error: any) {
    console.error('❌ Crime details error:', error);
    Alert.alert('Error', 'An error occurred while fetching crime details');
    return null;
  }
};

/**
 * Combined search function that handles all filter combinations
 * This is the main function to use from components
 */
export const performCrimeSearch = async (
  filters: FilterData,
  options?: {
    page?: number;
    limit?: number;
    severity?: 'low' | 'medium' | 'high';
    status?: 'reported' | 'investigating' | 'resolved' | 'closed';
  }
): Promise<{
  data: CrimeRecord[];
  pagination: any;
  hasFilters: boolean;
} | null> => {
  try {
    // Check if any filters are applied
    const hasFilters = !!(
      filters.fromDate || 
      filters.toDate || 
      filters.crimeType || 
      filters.city || 
      filters.area
    );

    let result: CrimeSearchResponse | null;

    if (hasFilters) {
      // Use filtered search
      result = await searchCrimes(filters, options);
    } else {
      // Use basic search for initial load
      result = await getBasicCrimeData(options?.limit || 10);
    }

    if (result) {
      return {
        data: result.data,
        pagination: result.pagination,
        hasFilters,
      };
    }

    return null;
  } catch (error: any) {
    console.error('❌ Combined search error:', error);
    return null;
  }
};
