import AsyncStorage from '@react-native-async-storage/async-storage';
import { AppState, Location } from '@types';
import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';

export const useAppStore = create<AppState>()(
  persist(
    (set) => ({
      isFirstLaunch: true,
      hasLocationPermission: false,
      currentLocation: null,

      setFirstLaunch: (isFirstLaunch: boolean) => {
        set({ isFirstLaunch });
      },

      setLocationPermission: (hasLocationPermission: boolean) => {
        set({ hasLocationPermission });
      },

      setCurrentLocation: (currentLocation: Location | null) => {
        set({ currentLocation });
      },
    }),
    {
      name: 'app-storage',
      storage: createJSONStorage(() => AsyncStorage),
    },
  ),
);
