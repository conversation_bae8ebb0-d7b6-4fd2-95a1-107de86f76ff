import { create } from 'zustand';
import { CrimeRecord, FilterData } from '@/types';

interface CrimeState {
  // Data
  crimes: CrimeRecord[];
  selectedCrime: CrimeRecord | null;
  
  // Pagination
  currentPage: number;
  totalPages: number;
  totalRecords: number;
  hasNext: boolean;
  hasPrevious: boolean;
  
  // Filters
  appliedFilters: FilterData | null;
  hasActiveFilters: boolean;
  
  // Loading states
  isLoading: boolean;
  isLoadingMore: boolean;
  isRefreshing: boolean;
  
  // Error handling
  error: string | null;
  
  // Actions
  setCrimes: (crimes: CrimeRecord[]) => void;
  addCrimes: (crimes: CrimeRecord[]) => void;
  setSelectedCrime: (crime: CrimeRecord | null) => void;
  setPagination: (pagination: {
    currentPage: number;
    totalPages: number;
    totalRecords: number;
    hasNext: boolean;
    hasPrevious: boolean;
  }) => void;
  setAppliedFilters: (filters: FilterData | null) => void;
  setLoading: (loading: boolean) => void;
  setLoadingMore: (loading: boolean) => void;
  setRefreshing: (refreshing: boolean) => void;
  setError: (error: string | null) => void;
  clearCrimes: () => void;
  clearFilters: () => void;
  reset: () => void;
}

const initialState = {
  crimes: [],
  selectedCrime: null,
  currentPage: 1,
  totalPages: 0,
  totalRecords: 0,
  hasNext: false,
  hasPrevious: false,
  appliedFilters: null,
  hasActiveFilters: false,
  isLoading: false,
  isLoadingMore: false,
  isRefreshing: false,
  error: null,
};

export const useCrimeStore = create<CrimeState>((set, get) => ({
  ...initialState,

  setCrimes: (crimes: CrimeRecord[]) => {
    set({ crimes, error: null });
  },

  addCrimes: (newCrimes: CrimeRecord[]) => {
    const { crimes } = get();
    const existingIds = new Set(crimes.map(crime => crime.id));
    const uniqueNewCrimes = newCrimes.filter(crime => !existingIds.has(crime.id));
    set({ crimes: [...crimes, ...uniqueNewCrimes] });
  },

  setSelectedCrime: (crime: CrimeRecord | null) => {
    set({ selectedCrime: crime });
  },

  setPagination: (pagination) => {
    set({
      currentPage: pagination.currentPage,
      totalPages: pagination.totalPages,
      totalRecords: pagination.totalRecords,
      hasNext: pagination.hasNext,
      hasPrevious: pagination.hasPrevious,
    });
  },

  setAppliedFilters: (filters: FilterData | null) => {
    const hasActiveFilters = !!(
      filters?.fromDate || 
      filters?.toDate || 
      filters?.crimeType || 
      filters?.city || 
      filters?.area
    );
    
    set({ 
      appliedFilters: filters, 
      hasActiveFilters,
    });
  },

  setLoading: (loading: boolean) => {
    set({ isLoading: loading });
  },

  setLoadingMore: (loading: boolean) => {
    set({ isLoadingMore: loading });
  },

  setRefreshing: (refreshing: boolean) => {
    set({ isRefreshing: refreshing });
  },

  setError: (error: string | null) => {
    set({ error });
  },

  clearCrimes: () => {
    set({ crimes: [], selectedCrime: null });
  },

  clearFilters: () => {
    set({ 
      appliedFilters: null, 
      hasActiveFilters: false,
    });
  },

  reset: () => {
    set(initialState);
  },
}));
