import AsyncStorage from '@react-native-async-storage/async-storage';
import { apiClient } from '@services/api';
import { AuthState, User } from '@types';
import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      isAuthenticated: false,
      token: null,
      user: null,
      isLoading: false,

      setToken: async (token: string) => {
        await apiClient.setAuthToken(token);
        await AsyncStorage.setItem('auth_token', token);
        set({ token, isAuthenticated: true });
      },

      setUser: (user: User) => {
        set({ user });
      },

      logout: async () => {
        await apiClient.removeAuthToken();
        await AsyncStorage.removeItem('auth_token');
        set({
          isAuthenticated: false,
          token: null,
          user: null,
        });
      },

      setLoading: (isLoading: boolean) => {
        set({ isLoading });
      },

      initializeAuth: async () => {
        try {
          const token = await AsyncStorage.getItem('auth_token');
          if (token) {
            await apiClient.setAuthToken(token);
            set({ token, isAuthenticated: true });
          }
        } catch (error) {
          console.error('Failed to initialize auth:', error);
        }
      },
    }),
    {
      name: 'auth-storage',
      storage: createJSONStorage(() => AsyncStorage),
      partialize: state => ({
        isAuthenticated: state.isAuthenticated,
        token: state.token,
        user: state.user,
      }),
    },
  ),
);
