// API Response Types
export interface ApiResponse<T = any> {
  status: boolean;
  status_code: number;
  data: T;
  message: string;
}

// User Types
export interface User {
  id: string;
  first_name: string;
  last_name: string;
  email_id: string;
  birth_date: string;
  mobile: string;
  profile_picture?: string;
  home_address?: string;
  latitude?: number;
  longitude?: number;
}

// Auth Types
export interface LoginRequest {
  mobile: string;
}

export interface VerifyOTPRequest {
  mobile: string;
  otp: string;
}

export interface ProfileSetupRequest {
  first_name: string;
  last_name: string;
  email_id: string;
  birth_date: string;
}

export interface AddressRequest {
  home_address: string;
  latitude: number;
  longitude: number;
}

// Location Types
export interface Location {
  latitude: number;
  longitude: number;
  address?: string;
  place_id?: string;
  pincode?: string;
}

export interface GooglePlaceResult {
  description: string;
  place_id: string;
  structured_formatting: {
    main_text: string;
    secondary_text: string;
  };
}

// Navigation Types
export type RootStackParamList = {
  Splash: undefined;
  GetStarted: undefined;
  Login: undefined;
  VerifyOTP: { mobile: string };
  ProfileSetup: undefined;
  AddProfilePic: undefined;
  LocationPermission: undefined;
  AddHomeAddress: undefined;
  MainTabs: undefined;
};

export type MainTabParamList = {
  Home: undefined;
  Search: undefined;
  Safety: undefined;
  Notifications: undefined;
  Profile: undefined;
};

export type HomeStackParamList = {
  HomeMain: undefined;
  Filter: { onFilterApply: (filters: FilterData) => void };
};

// Form Types
export interface FormField {
  value: string;
  error?: string;
  touched?: boolean;
}

export interface LoginForm {
  countryCode: FormField;
  mobile: FormField;
  termsAccepted: boolean;
}

export interface ProfileForm {
  firstName: FormField;
  lastName: FormField;
  email: FormField;
  birthDate: FormField;
}

// Filter Types
export interface FilterData {
  fromDate: string;
  toDate: string;
  crimeType: string;
  city: string;
  area: string;
}

export interface CityData {
  [province: string]: {
    [city: string]: string[];
  };
}

// Store Types
export interface AuthState {
  isAuthenticated: boolean;
  token: string | null;
  user: User | null;
  isLoading: boolean;
  setToken: (token: string) => void;
  setUser: (user: User) => void;
  logout: () => void;
  setLoading: (loading: boolean) => void;
  initializeAuth: () => Promise<void>;
}

export interface AppState {
  isFirstLaunch: boolean;
  hasLocationPermission: boolean;
  currentLocation: Location | null;
  setFirstLaunch: (isFirst: boolean) => void;
  setLocationPermission: (hasPermission: boolean) => void;
  setCurrentLocation: (location: Location | null) => void;
}
