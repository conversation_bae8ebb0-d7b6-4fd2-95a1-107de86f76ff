import { NavigationContainer } from '@react-navigation/native';
import { createNativeStackNavigator } from '@react-navigation/native-stack';

// Import screens
import LoginScreen from '@screens/auth/LoginScreen';
import VerifyOTPScreen from '@screens/auth/VerifyOTPScreen';
import GetStartedScreen from '@screens/GetStartedScreen';
import AddHomeAddressScreen from '@screens/onboarding/AddHomeAddressScreen';
import AddProfilePicScreen from '@screens/onboarding/AddProfilePicScreen';
import LocationPermissionScreen from '@screens/onboarding/LocationPermissionScreen';
import ProfileSetupScreen from '@screens/onboarding/ProfileSetupScreen';
import SplashScreen from '@screens/SplashScreen';
import { useAppStore } from '@store/appStore';
import { useAuthStore } from '@store/authStore';
import { RootStackParamList } from '@types';
import React from 'react';

import MainTabNavigator from './MainTabNavigator';

const Stack = createNativeStackNavigator<RootStackParamList>();

export const AppNavigator: React.FC = () => {
  const { isAuthenticated, user } = useAuthStore();
  const { isFirstLaunch } = useAppStore();

  const getInitialRouteName = (): keyof RootStackParamList => {
    if (isFirstLaunch) {
      return 'Splash';
    }

    if (!isAuthenticated) {
      return 'GetStarted';
    }

    // Check if user has completed onboarding
    if (!user?.first_name) {
      return 'ProfileSetup';
    }

    if (!user?.profile_picture) {
      return 'AddProfilePic';
    }

    if (!user?.home_address) {
      return 'AddHomeAddress';
    }

    return 'MainTabs';
  };

  return (
    <NavigationContainer>
      <Stack.Navigator
        initialRouteName={getInitialRouteName()}
        screenOptions={{
          headerShown: false,
          gestureEnabled: false,
        }}>
        <Stack.Screen name="Splash" component={SplashScreen} />
        <Stack.Screen name="GetStarted" component={GetStartedScreen} />
        <Stack.Screen name="Login" component={LoginScreen} />
        <Stack.Screen name="VerifyOTP" component={VerifyOTPScreen} />
        <Stack.Screen name="ProfileSetup" component={ProfileSetupScreen} />
        <Stack.Screen name="AddProfilePic" component={AddProfilePicScreen} />
        <Stack.Screen
          name="LocationPermission"
          component={LocationPermissionScreen}
        />
        <Stack.Screen name="AddHomeAddress" component={AddHomeAddressScreen} />
        <Stack.Screen name="MainTabs" component={MainTabNavigator} />
      </Stack.Navigator>
    </NavigationContainer>
  );
};
