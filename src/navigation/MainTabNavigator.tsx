import RippleEffect from '@components/ui/RippleEffect';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import HomeStackNavigator from '@navigation/HomeStackNavigator';
import NotificationsScreen from '@screens/main/NotificationsScreen';
import ProfileScreen from '@screens/main/ProfileScreen';
import SafetyScreen from '@screens/main/SafetyScreen';
import SearchScreen from '@screens/main/SearchScreen';
import { Colors, Spacing } from '@utils/theme';
import React, { useEffect, useRef } from 'react';
import { View, Animated, StyleSheet, Platform } from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';

import { MainTabParamList } from '@/types';
import { Typography } from '@/utils/typography';
interface AnimatedTabBarProps {
  state: any;
  navigation: any;
}

const AnimatedTabBar: React.FC<AnimatedTabBarProps> = ({
  state,
  navigation,
}) => {
  const animatedValues = useRef(
    state.routes.map(() => new Animated.Value(0)),
  ).current;

  useEffect(() => {
    animatedValues.forEach((animatedValue: Animated.Value, index: number) => {
      Animated.timing(animatedValue, {
        toValue: state.index === index ? 1 : 0,
        duration: 300,
        useNativeDriver: true,
      }).start();
    });
  }, [state.index, animatedValues]);

  const getTabIcon = (routeName: string, focused: boolean) => {
    let iconName: string;
    let iconColor: string;

    switch (routeName) {
      case 'Home':
        iconName = focused ? 'home' : 'home-outline';
        iconColor = focused ? Colors.primary : Colors.text.secondary;
        break;
      case 'Search':
        iconName = 'magnify';
        iconColor = focused ? Colors.primary : Colors.text.secondary;
        break;
      case 'Safety':
        iconName = focused ? 'shield-check' : 'shield-check-outline';
        iconColor = focused ? Colors.success : Colors.text.secondary;
        break;
      case 'Notifications':
        iconName = focused ? 'bell' : 'bell-outline';
        iconColor = focused ? Colors.warning : Colors.text.secondary;
        break;
      case 'Profile':
        iconName = focused ? 'account' : 'account-outline';
        iconColor = focused ? Colors.primary : Colors.text.secondary;
        break;
      default:
        iconName = 'circle';
        iconColor = Colors.text.secondary;
    }

    return { iconName, iconColor };
  };

  const getTabLabel = (routeName: string) => {
    switch (routeName) {
      case 'Home':
        return 'Location';
      case 'Search':
        return 'Search';
      case 'Safety':
        return 'Safety';
      case 'Notifications':
        return 'Alerts';
      case 'Profile':
        return 'Profile';
      default:
        return routeName;
    }
  };

  return (
    <View style={styles.tabBarContainer}>
      <LinearGradient
        colors={[
          'rgba(255, 255, 255, 0.98)',
          'rgba(248, 250, 252, 1)',
          'rgba(241, 245, 249, 1)',
        ]}
        style={styles.tabBarGradient}
        start={{ x: 0, y: 0 }}
        end={{ x: 0, y: 1 }}
      />

      <View style={styles.tabBar}>
        {state.routes.map((route: any, index: number) => {
          const isFocused = state.index === index;
          const { iconName, iconColor } = getTabIcon(route.name, isFocused);
          const label = getTabLabel(route.name);

          const onPress = () => {
            const event = navigation.emit({
              type: 'tabPress',
              target: route.key,
              canPreventDefault: true,
            });

            if (!isFocused && !event.defaultPrevented) {
              navigation.navigate(route.name);
            }
          };

          const onLongPress = () => {
            navigation.emit({
              type: 'tabLongPress',
              target: route.key,
            });
          };

          const scale = animatedValues[index].interpolate({
            inputRange: [0, 1],
            outputRange: [1, 1.15],
          });

          const translateY = animatedValues[index].interpolate({
            inputRange: [0, 1],
            outputRange: [0, -4],
          });

          const iconOpacity = animatedValues[index].interpolate({
            inputRange: [0, 1],
            outputRange: [0.6, 1],
          });

          const labelOpacity = animatedValues[index].interpolate({
            inputRange: [0, 1],
            outputRange: [0.5, 1],
          });

          return (
            <RippleEffect
              key={route.key}
              onPress={onPress}
              onLongPress={onLongPress}
              style={styles.tabItem}
              rippleColor={iconColor}
              rippleOpacity={0.2}
              rippleDuration={400}>
              <Animated.View
                style={[
                  styles.iconContainer,
                  {
                    transform: [{ scale }, { translateY }],
                    opacity: iconOpacity,
                  },
                ]}>
                <Icon name={iconName} size={26} color={iconColor} />
              </Animated.View>

              <Animated.Text
                style={[
                  styles.tabLabel,
                  {
                    color: iconColor,
                    opacity: labelOpacity,
                    transform: [
                      {
                        scale: animatedValues[index].interpolate({
                          inputRange: [0, 1],
                          outputRange: [0.9, 1],
                        }),
                      },
                    ],
                  },
                ]}>
                {label}
              </Animated.Text>
            </RippleEffect>
          );
        })}
      </View>
    </View>
  );
};

const Tab = createBottomTabNavigator<MainTabParamList>();

const MainTabNavigator: React.FC = () => {
  return (
    <Tab.Navigator
      tabBar={props => <AnimatedTabBar {...props} />}
      screenOptions={{
        headerShown: false,
      }}>
      <Tab.Screen name="Home" component={HomeStackNavigator} />
      <Tab.Screen name="Search" component={SearchScreen} />
      <Tab.Screen name="Safety" component={SafetyScreen} />
      <Tab.Screen name="Notifications" component={NotificationsScreen} />
      <Tab.Screen name="Profile" component={ProfileScreen} />
    </Tab.Navigator>
  );
};

const styles = StyleSheet.create({
  tabBarContainer: {
    position: 'relative',
    backgroundColor: 'white',
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -4 },
    shadowOpacity: 0.15,
    shadowRadius: 12,
    elevation: 20,
  },
  tabBarGradient: {
    position: 'absolute',
    left: 0,
    right: 0,
    top: 0,
    bottom: 0,
  },
  tabBar: {
    flexDirection: 'row',
    height: Platform.OS === 'ios' ? 85 : 70,
    marginTop: 5,
    paddingHorizontal: Spacing.md,
    backgroundColor: 'transparent',
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
  },
  tabItem: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 100,
    position: 'relative',
    overflow: 'hidden',
    marginHorizontal: 2,
  },
  iconContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 3,
    position: 'relative',
    width: 32,
    height: 32,
  },
  tabLabel: {
    ...Typography.caption,
    fontSize: 10,
    fontWeight: '600',
    textAlign: 'center',
    marginTop: 5,
    letterSpacing: 0.2,
  },
});

export default MainTabNavigator;
