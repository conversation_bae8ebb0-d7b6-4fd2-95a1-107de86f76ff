import React, { useRef } from 'react';
import {
  Animated,
  StyleSheet,
  View,
  TouchableWithoutFeedback,
} from 'react-native';

interface RippleEffectProps {
  children: React.ReactNode;
  onPress?: () => void;
  onLongPress?: () => void;
  rippleColor?: string;
  rippleOpacity?: number;
  rippleDuration?: number;
  style?: any;
  disabled?: boolean;
}

const RippleEffect: React.FC<RippleEffectProps> = ({
  children,
  onPress,
  onLongPress,
  rippleColor = 'rgba(255, 255, 255, 0.3)',
  rippleOpacity = 0.3,
  rippleDuration = 600,
  style,
  disabled = false,
}) => {
  const scaleValue = useRef(new Animated.Value(0)).current;
  const opacityValue = useRef(new Animated.Value(1)).current;

  const startRipple = () => {
    if (disabled) return;

    scaleValue.setValue(0);
    opacityValue.setValue(rippleOpacity);

    Animated.parallel([
      Animated.timing(scaleValue, {
        toValue: 1,
        duration: rippleDuration,
        useNativeDriver: true,
      }),
      Animated.timing(opacityValue, {
        toValue: 0,
        duration: rippleDuration,
        useNativeDriver: true,
      }),
    ]).start();

    if (onPress) {
      setTimeout(onPress, 100);
    }
  };

  const handleLongPress = () => {
    if (disabled) return;
    if (onLongPress) {
      onLongPress();
    }
  };

  return (
    <TouchableWithoutFeedback
      onPress={startRipple}
      onLongPress={handleLongPress}
      disabled={disabled}>
      <View style={[styles.container, style]}>
        {children}
        <Animated.View
          style={[
            styles.ripple,
            {
              backgroundColor: rippleColor,
              transform: [{ scale: scaleValue }],
              opacity: opacityValue,
            },
          ]}
        />
      </View>
    </TouchableWithoutFeedback>
  );
};

const styles = StyleSheet.create({
  container: {
    overflow: 'hidden',
    position: 'relative',
  },
  ripple: {
    position: 'absolute',
    top: -10,
    left: -10,
    right: -10,
    bottom: -10,
    borderRadius: 1000,
  },
});

export default RippleEffect;
