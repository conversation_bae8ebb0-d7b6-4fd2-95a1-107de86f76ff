import { BorderRadius, Colors, Spacing } from '@utils/theme';
import { Typography } from '@utils/typography';
import React from 'react';
import { StyleSheet, TextStyle, ViewStyle } from 'react-native';
import {
  Button as PaperButton,
  ButtonProps as PaperButtonProps,
} from 'react-native-paper';

interface ButtonProps extends Omit<PaperButtonProps, 'children'> {
  title: string;
  variant?: 'contained' | 'outlined' | 'text';
  size?: 'small' | 'medium' | 'large';
  fullWidth?: boolean;
  loading?: boolean;
  disabled?: boolean;
  onPress?: () => void;
  style?: ViewStyle;
  textStyle?: TextStyle;
}

export const Button: React.FC<ButtonProps> = ({
  title,
  variant = 'contained',
  size = 'small',
  fullWidth = false,
  loading = false,
  disabled = false,
  onPress,
  style,
  textStyle,
  ...props
}) => {
  const buttonStyle = [
    styles.base,
    styles[size],
    fullWidth && styles.fullWidth,
    style,
  ];

  const contentStyle = [styles.content, styles[`${size}Content`]];

  return (
    <PaperButton
      mode={variant}
      onPress={onPress}
      loading={loading}
      disabled={disabled || loading}
      style={buttonStyle}
      contentStyle={contentStyle}
      labelStyle={[styles.label, styles[`${size}Label`], textStyle]}
      {...props}>
      {title}
    </PaperButton>
  );
};

const styles = StyleSheet.create({
  base: {
    borderRadius: BorderRadius.md,
  },
  fullWidth: {
    width: '100%',
  },
  content: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  label: {
    ...Typography.button,
    color: Colors.white,
  },
  // Size variants
  small: {
    minHeight: 36,
  },
  medium: {
    minHeight: 48,
  },
  large: {
    minHeight: 56,
  },
  smallContent: {
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.xs,
  },
  mediumContent: {
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.sm,
  },
  largeContent: {
    paddingHorizontal: Spacing.xl,
    paddingVertical: Spacing.md,
  },
  smallLabel: {
    fontSize: 14,
  },
  mediumLabel: {
    fontSize: 16,
  },
  largeLabel: {
    fontSize: 18,
  },
});
