import { Colors, Spacing } from '@utils/theme';
import { Typography } from '@utils/typography';
import React, { useState } from 'react';
import { Alert, ScrollView, StyleSheet, View } from 'react-native';
import { Text } from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';

import { RippleButton } from './RippleButton';

export const RippleButtonDemo: React.FC = () => {
  const [loading, setLoading] = useState(false);

  const handlePress = (buttonType: string) => {
    // Alert.alert('Button Pressed', `You pressed the ${buttonType} button!`);
  };

  const handleLoadingPress = () => {
    setLoading(true);
    setTimeout(() => {
      setLoading(false);
      Alert.alert('Success', 'Loading completed!');
    }, 3000);
  };

  return (
    <ScrollView style={styles.container} contentContainerStyle={styles.content}>
      <Text style={styles.title}>RippleButton Demo</Text>
      <Text style={styles.subtitle}>
        Custom ripple animation button component without external libraries
      </Text>

      {/* Basic Variants */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Basic Variants</Text>

        <RippleButton
          title="Contained Button"
          variant="contained"
          onPress={() => handlePress('contained')}
          style={styles.button}
        />

        <RippleButton
          title="Outlined Button"
          variant="outlined"
          onPress={() => handlePress('outlined')}
          style={styles.button}
        />

        <RippleButton
          title="Text Button"
          variant="text"
          onPress={() => handlePress('text')}
          style={styles.button}
        />
      </View>

      {/* Sizes */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Sizes</Text>

        <RippleButton
          title="Small Button"
          size="small"
          onPress={() => handlePress('small')}
          style={styles.button}
        />

        <RippleButton
          title="Medium Button"
          size="medium"
          onPress={() => handlePress('medium')}
          style={styles.button}
        />

        <RippleButton
          title="Large Button"
          size="large"
          onPress={() => handlePress('large')}
          style={styles.button}
        />
      </View>

      {/* States */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>States</Text>

        <RippleButton
          title="Normal Button"
          onPress={() => handlePress('normal')}
          style={styles.button}
        />

        <RippleButton
          title="Disabled Button"
          disabled={true}
          onPress={() => handlePress('disabled')}
          style={styles.button}
        />

        <RippleButton
          title={loading ? 'Loading...' : 'Loading Button'}
          loading={loading}
          onPress={handleLoadingPress}
          style={styles.button}
        />
      </View>

      {/* Custom Ripple Colors */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Custom Ripple Colors</Text>

        <RippleButton
          title="Red Ripple"
          variant="contained"
          rippleColor="rgba(255, 0, 0, 0.3)"
          onPress={() => handlePress('red ripple')}
          style={[styles.button, { backgroundColor: Colors.error }]}
        />

        <RippleButton
          title="Green Ripple"
          variant="contained"
          rippleColor="rgba(0, 255, 0, 0.3)"
          onPress={() => handlePress('green ripple')}
          style={[styles.button, { backgroundColor: Colors.success }]}
        />

        <RippleButton
          title="Blue Ripple"
          variant="outlined"
          rippleColor="rgba(0, 0, 255, 0.2)"
          onPress={() => handlePress('blue ripple')}
          style={[styles.button, { borderColor: Colors.info }]}
          textStyle={{ color: Colors.info }}
        />
      </View>

      {/* Full Width */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Full Width</Text>

        <RippleButton
          title="Full Width Button"
          fullWidth={true}
          onPress={() => handlePress('full width')}
          style={styles.button}
        />
      </View>

      {/* Custom Content */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Custom Content</Text>

        <RippleButton
          title=""
          onPress={() => handlePress('icon')}
          style={styles.button}>
          <View style={styles.iconButtonContent}>
            <Icon name="heart" size={20} color={Colors.white} />
            <Text style={styles.iconButtonText}>Like</Text>
          </View>
        </RippleButton>

        <RippleButton
          title=""
          variant="outlined"
          onPress={() => handlePress('icon outlined')}
          style={styles.button}>
          <View style={styles.iconButtonContent}>
            <Icon name="share" size={20} color={Colors.primary} />
            <Text style={[styles.iconButtonText, { color: Colors.primary }]}>
              Share
            </Text>
          </View>
        </RippleButton>
      </View>

      {/* Fast Ripple */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Custom Animation Duration</Text>

        <RippleButton
          title="Fast Ripple (300ms)"
          rippleDuration={300}
          onPress={() => handlePress('fast ripple')}
          style={styles.button}
        />

        <RippleButton
          title="Slow Ripple (1000ms)"
          rippleDuration={1000}
          onPress={() => handlePress('slow ripple')}
          style={styles.button}
        />
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  content: {
    padding: Spacing.lg,
  },
  title: {
    ...Typography.h2,
    color: Colors.text.primary,
    marginBottom: Spacing.sm,
    textAlign: 'center',
  },
  subtitle: {
    ...Typography.body1,
    color: Colors.text.secondary,
    textAlign: 'center',
    marginBottom: Spacing.xl,
    lineHeight: 24,
  },
  section: {
    marginBottom: Spacing.xl,
  },
  sectionTitle: {
    ...Typography.h3,
    color: Colors.text.primary,
    marginBottom: Spacing.md,
  },
  button: {
    marginBottom: Spacing.md,
  },
  iconButtonContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  iconButtonText: {
    ...Typography.button,
    color: Colors.white,
    marginLeft: Spacing.sm,
  },
});
