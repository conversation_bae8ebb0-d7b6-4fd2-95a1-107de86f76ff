import {
  requestCameraPermission,
  requestPhotoLibraryPermission,
} from '@utils/permissions';
import { BorderRadius, Colors, Spacing } from '@utils/theme';
import React from 'react';
import {
  Alert,
  StyleSheet,
  TouchableOpacity,
  View,
  ViewStyle,
} from 'react-native';
import ImageCropPicker, {
  Image as CropPickerImage,
} from 'react-native-image-crop-picker';
import { Avatar, Text } from 'react-native-paper';

import { Typography } from '@/utils/typography';

interface ImagePickerProps {
  imageUri?: string;
  onImageSelect: (imageUri: string) => void;
  size?: number;
  containerStyle?: ViewStyle;
  placeholder?: string;
}

export const ImagePicker: React.FC<ImagePickerProps> = ({
  imageUri,
  onImageSelect,
  size = 120,
  containerStyle,
  placeholder = 'Tap to add photo',
}) => {
  const showImagePicker = () => {
    Alert.alert(
      'Select Image',
      'Choose an option to select your profile picture',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Camera', onPress: openCamera },
        { text: 'Gallery', onPress: openGallery },
      ],
      { cancelable: true },
    );
  };

  const openCamera = async () => {
    const hasPermission = await requestCameraPermission();
    if (!hasPermission) {
      return;
    }

    try {
      const image: CropPickerImage = await ImageCropPicker.openCamera({
        width: 200,
        height: 200,
        cropping: true,
        cropperCircleOverlay: true,
        compressImageQuality: 0.8,
        mediaType: 'photo',
        includeBase64: false,
      });

      onImageSelect(image.path);
    } catch (error) {
      console.log('Camera picker cancelled or error:', error);
    }
  };

  const openGallery = async () => {
    const hasPermission = await requestPhotoLibraryPermission();
    if (!hasPermission) {
      return;
    }

    try {
      const image: CropPickerImage = await ImageCropPicker.openPicker({
        width: 200,
        height: 200,
        cropping: true,
        cropperCircleOverlay: true,
        compressImageQuality: 0.8,
        mediaType: 'photo',
        includeBase64: false,
      });

      onImageSelect(image.path);
    } catch (error) {
      console.log('Gallery picker cancelled or error:', error);
    }
  };

  return (
    <View style={[styles.container, containerStyle]}>
      <TouchableOpacity onPress={showImagePicker} style={styles.touchable}>
        {imageUri ? (
          <Avatar.Image
            size={size}
            source={{ uri: imageUri }}
            style={styles.avatar}
          />
        ) : (
          <View style={[styles.placeholder, { width: size, height: size }]}>
            <Avatar.Icon
              size={size * 0.6}
              icon="camera"
              style={styles.cameraIcon}
            />
          </View>
        )}
      </TouchableOpacity>
      <Text style={styles.placeholderText}>{placeholder}</Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  touchable: {
    borderRadius: BorderRadius.full,
    overflow: 'hidden',
  },
  avatar: {
    backgroundColor: Colors.surface,
  },
  placeholder: {
    backgroundColor: Colors.surface,
    borderRadius: BorderRadius.full,
    borderWidth: 2,
    borderColor: Colors.border,
    borderStyle: 'dashed',
    alignItems: 'center',
    justifyContent: 'center',
  },
  cameraIcon: {
    backgroundColor: Colors.primary,
  },
  placeholderText: {
    ...Typography.body2,
    color: Colors.text.secondary,
    marginTop: Spacing.sm,
    textAlign: 'center',
  },
});
