import React, { useRef, useState } from 'react';
import {
  Animated,
  Pressable,
  StyleSheet,
  Text,
  View,
  ViewStyle,
  TextStyle,
  GestureResponderEvent,
  LayoutChangeEvent,
} from 'react-native';
import { Colors, BorderRadius, Spacing } from '@utils/theme';
import { Typography } from '@utils/typography';

interface RippleButtonProps {
  title: string;
  onPress?: () => void;
  variant?: 'contained' | 'outlined' | 'text';
  size?: 'small' | 'medium' | 'large';
  fullWidth?: boolean;
  disabled?: boolean;
  loading?: boolean;
  style?: ViewStyle;
  textStyle?: TextStyle;
  rippleColor?: string;
  rippleDuration?: number;
  children?: React.ReactNode;
}

interface RippleData {
  id: number;
  x: number;
  y: number;
  scale: Animated.Value;
  opacity: Animated.Value;
}

export const RippleButton: React.FC<RippleButtonProps> = ({
  title,
  onPress,
  variant = 'contained',
  size = 'medium',
  fullWidth = false,
  disabled = false,
  loading = false,
  style,
  textStyle,
  rippleColor,
  rippleDuration = 600,
  children,
}) => {
  const [ripples, setRipples] = useState<RippleData[]>([]);
  const [buttonLayout, setButtonLayout] = useState({ width: 0, height: 0 });
  const rippleIdRef = useRef(0);

  const getRippleColor = () => {
    if (rippleColor) return rippleColor;
    
    switch (variant) {
      case 'contained':
        return 'rgba(255, 255, 255, 0.3)';
      case 'outlined':
      case 'text':
        return 'rgba(0, 0, 0, 0.1)';
      default:
        return 'rgba(255, 255, 255, 0.3)';
    }
  };

  const createRipple = (event: GestureResponderEvent) => {
    if (disabled || loading) return;

    const { locationX, locationY } = event.nativeEvent;
    const rippleId = rippleIdRef.current++;

    // Calculate the maximum distance from touch point to button edges
    const maxDistance = Math.max(
      Math.sqrt(locationX ** 2 + locationY ** 2),
      Math.sqrt((buttonLayout.width - locationX) ** 2 + locationY ** 2),
      Math.sqrt(locationX ** 2 + (buttonLayout.height - locationY) ** 2),
      Math.sqrt(
        (buttonLayout.width - locationX) ** 2 + (buttonLayout.height - locationY) ** 2
      )
    );

    const scale = new Animated.Value(0);
    const opacity = new Animated.Value(1);

    const newRipple: RippleData = {
      id: rippleId,
      x: locationX,
      y: locationY,
      scale,
      opacity,
    };

    setRipples(prev => [...prev, newRipple]);

    // Start ripple animation
    Animated.parallel([
      Animated.timing(scale, {
        toValue: maxDistance * 2,
        duration: rippleDuration,
        useNativeDriver: true,
      }),
      Animated.timing(opacity, {
        toValue: 0,
        duration: rippleDuration,
        useNativeDriver: true,
      }),
    ]).start(() => {
      // Remove ripple after animation completes
      setRipples(prev => prev.filter(ripple => ripple.id !== rippleId));
    });

    // Call onPress after a short delay to let ripple start
    if (onPress) {
      setTimeout(onPress, 100);
    }
  };

  const handleLayout = (event: LayoutChangeEvent) => {
    const { width, height } = event.nativeEvent.layout;
    setButtonLayout({ width, height });
  };

  const getButtonStyle = (): ViewStyle[] => {
    const baseStyle = [styles.base, styles[size]];
    
    if (fullWidth) baseStyle.push(styles.fullWidth);
    if (disabled) baseStyle.push(styles.disabled);
    
    switch (variant) {
      case 'contained':
        baseStyle.push(styles.contained);
        break;
      case 'outlined':
        baseStyle.push(styles.outlined);
        break;
      case 'text':
        baseStyle.push(styles.text);
        break;
    }
    
    if (style) baseStyle.push(style);
    
    return baseStyle;
  };

  const getTextStyle = (): TextStyle[] => {
    const baseTextStyle = [styles.baseText, styles[`${size}Text`]];
    
    switch (variant) {
      case 'contained':
        baseTextStyle.push(styles.containedText);
        break;
      case 'outlined':
        baseTextStyle.push(styles.outlinedText);
        break;
      case 'text':
        baseTextStyle.push(styles.textText);
        break;
    }
    
    if (disabled) baseTextStyle.push(styles.disabledText);
    if (textStyle) baseTextStyle.push(textStyle);
    
    return baseTextStyle;
  };

  return (
    <Pressable
      style={getButtonStyle()}
      onPress={createRipple}
      onLayout={handleLayout}
      disabled={disabled || loading}
      android_ripple={undefined} // Disable default Android ripple
    >
      <View style={styles.content}>
        {children || <Text style={getTextStyle()}>{title}</Text>}
        
        {/* Loading indicator */}
        {loading && (
          <View style={styles.loadingContainer}>
            <Text style={[getTextStyle(), styles.loadingText]}>Loading...</Text>
          </View>
        )}
      </View>

      {/* Custom ripple effects */}
      <View style={styles.rippleContainer} pointerEvents="none">
        {ripples.map(ripple => (
          <Animated.View
            key={ripple.id}
            style={[
              styles.ripple,
              {
                left: ripple.x,
                top: ripple.y,
                backgroundColor: getRippleColor(),
                transform: [{ scale: ripple.scale }],
                opacity: ripple.opacity,
              },
            ]}
          />
        ))}
      </View>
    </Pressable>
  );
};

const styles = StyleSheet.create({
  base: {
    borderRadius: BorderRadius.md,
    justifyContent: 'center',
    alignItems: 'center',
    overflow: 'hidden',
    position: 'relative',
  },
  fullWidth: {
    width: '100%',
  },
  disabled: {
    opacity: 0.6,
  },
  
  // Size variants
  small: {
    minHeight: 36,
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.xs,
  },
  medium: {
    minHeight: 48,
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.sm,
  },
  large: {
    minHeight: 56,
    paddingHorizontal: Spacing.xl,
    paddingVertical: Spacing.md,
  },
  
  // Variant styles
  contained: {
    backgroundColor: Colors.primary,
    elevation: 2,
    shadowColor: Colors.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  outlined: {
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: Colors.primary,
  },
  text: {
    backgroundColor: 'transparent',
  },
  
  // Content container
  content: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    zIndex: 1,
  },
  
  // Text styles
  baseText: {
    ...Typography.button,
    textAlign: 'center',
  },
  smallText: {
    fontSize: 14,
  },
  mediumText: {
    fontSize: 16,
  },
  largeText: {
    fontSize: 18,
  },
  containedText: {
    color: Colors.white,
  },
  outlinedText: {
    color: Colors.primary,
  },
  textText: {
    color: Colors.primary,
  },
  disabledText: {
    opacity: 0.6,
  },
  
  // Loading styles
  loadingContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.1)',
  },
  loadingText: {
    fontSize: 14,
  },
  
  // Ripple styles
  rippleContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    overflow: 'hidden',
    borderRadius: BorderRadius.md,
  },
  ripple: {
    position: 'absolute',
    width: 1,
    height: 1,
    borderRadius: 0.5,
    transform: [{ translateX: -0.5 }, { translateY: -0.5 }],
  },
});
