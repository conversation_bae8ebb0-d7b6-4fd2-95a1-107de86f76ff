import { placesAPI } from '@services/endpoints';
import { GooglePlaceResult, Location } from '@types';
import { BorderRadius, Colors, Spacing } from '@utils/theme';
import { debounce } from 'lodash';
import React, { useCallback, useRef, useState } from 'react';
import {
  FlatList,
  Keyboard,
  StyleSheet,
  TouchableOpacity,
  View,
  ViewStyle,
} from 'react-native';
import { ActivityIndicator, Card, Text, TextInput } from 'react-native-paper';

import { Typography } from '@/utils/typography';

interface GoogleAutocompleteProps {
  placeholder?: string;
  value: string;
  onChangeText: (text: string) => void;
  onPlaceSelect: (location: Location) => void;
  containerStyle?: ViewStyle;
  error?: string;
  label?: string;
}

export const GoogleAutocomplete: React.FC<GoogleAutocompleteProps> = ({
  placeholder = 'Search for a location...',
  value,
  onChangeText,
  onPlaceSelect,
  containerStyle,
  error,
  label,
}) => {
  const [predictions, setPredictions] = useState<GooglePlaceResult[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [showPredictions, setShowPredictions] = useState(false);
  const inputRef = useRef<any>(null);

  const searchPlaces = useCallback(
    debounce(async (input: string) => {
      if (input.length < 3) {
        setPredictions([]);
        setShowPredictions(false);
        setIsLoading(false);
        return;
      }

      try {
        setIsLoading(true);
        const response = await placesAPI.autocomplete(input);

        if (response.status === 'OK' && response.predictions) {
          setPredictions(response.predictions);
          setShowPredictions(true);
        } else {
          setPredictions([]);
          setShowPredictions(false);
        }
      } catch (error) {
        console.error('Error fetching places:', error);
        setPredictions([]);
        setShowPredictions(false);
      } finally {
        setIsLoading(false);
      }
    }, 300),
    [],
  );

  const handleTextChange = (text: string) => {
    onChangeText(text);
    searchPlaces(text);
  };

  const handlePlaceSelect = async (place: GooglePlaceResult) => {
    try {
      setIsLoading(true);
      const details = await placesAPI.getPlaceDetails(place.place_id);

      if (details.status === 'OK' && details.result) {
        const result = details.result;
        const location: Location = {
          latitude: result.geometry.location.lat,
          longitude: result.geometry.location.lng,
          address: result.formatted_address,
          place_id: place.place_id,
          pincode: extractPincode(result.address_components),
        };

        onChangeText(result.formatted_address);
        onPlaceSelect(location);
        setShowPredictions(false);
        Keyboard.dismiss();
      }
    } catch (error) {
      console.error('Error fetching place details:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const extractPincode = (addressComponents: any[]): string | undefined => {
    const pincodeComponent = addressComponents.find(component =>
      component.types.includes('postal_code'),
    );
    return pincodeComponent?.long_name;
  };

  const renderPrediction = ({ item }: {item: GooglePlaceResult}) => (
    <TouchableOpacity
      style={styles.predictionItem}
      onPress={() => handlePlaceSelect(item)}>
      <Text style={styles.predictionMain}>
        {item.structured_formatting.main_text}
      </Text>
      <Text style={styles.predictionSecondary}>
        {item.structured_formatting.secondary_text}
      </Text>
    </TouchableOpacity>
  );

  return (
    <View style={[styles.container, containerStyle]}>
      <TextInput
        ref={inputRef}
        label={label}
        placeholder={placeholder}
        value={value}
        onChangeText={handleTextChange}
        onFocus={() => value.length >= 3 && setShowPredictions(true)}
        error={!!error}
        mode="outlined"
        style={styles.input}
        right={
          isLoading ? (
            <TextInput.Icon icon={() => <ActivityIndicator size="small" />} />
          ) : (
            <TextInput.Icon icon="map-marker" />
          )
        }
      />

      {showPredictions && predictions.length > 0 && (
        <Card style={styles.predictionsContainer}>
          <FlatList
            data={predictions}
            renderItem={renderPrediction}
            keyExtractor={item => item.place_id}
            style={styles.predictionsList}
            keyboardShouldPersistTaps="handled"
            showsVerticalScrollIndicator={false}
          />
        </Card>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'relative',
    zIndex: 1000,
  },
  input: {
    backgroundColor: Colors.white,
  },
  predictionsContainer: {
    position: 'absolute',
    top: '100%',
    left: 0,
    right: 0,
    maxHeight: 200,
    backgroundColor: Colors.white,
    borderRadius: BorderRadius.md,
    elevation: 4,
    shadowColor: Colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    zIndex: 1001,
  },
  predictionsList: {
    maxHeight: 200,
  },
  predictionItem: {
    padding: Spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: Colors.divider,
  },
  predictionMain: {
    ...Typography.body1,
    color: Colors.text.primary,
    marginBottom: Spacing.xs,
  },
  predictionSecondary: {
    ...Typography.body2,
    color: Colors.text.secondary,
  },
});
