import { BorderRadius, Colors, Spacing } from '@utils/theme';
import React, { useRef, useState } from 'react';
import { StyleSheet, TextInput, View, ViewStyle } from 'react-native';
import { Text } from 'react-native-paper';

import { Typography } from '@/utils/typography';

interface OTPInputProps {
  value: string;
  onCodeChanged: (code: string) => void;
  onCodeFilled?: (code: string) => void;
  numberOfDigits?: number;
  error?: string;
  containerStyle?: ViewStyle;
}

export const OTPInput: React.FC<OTPInputProps> = ({
  value,
  onCodeChanged,
  onCodeFilled,
  numberOfDigits = 6,
  error,
  containerStyle,
}) => {
  const [focusedIndex, setFocusedIndex] = useState(0);
  const inputRefs = useRef<TextInput[]>([]);

  const handleTextChange = (text: string, index: number) => {
    const newCode = value.split('');
    newCode[index] = text;
    const updatedCode = newCode.join('');

    onCodeChanged(updatedCode);

    if (text && index < numberOfDigits - 1) {
      inputRefs.current[index + 1]?.focus();
      setFocusedIndex(index + 1);
    }

    if (updatedCode.length === numberOfDigits && onCodeFilled) {
      onCodeFilled(updatedCode);
    }
  };

  const handleKeyPress = (key: string, index: number) => {
    if (key === 'Backspace' && !value[index] && index > 0) {
      inputRefs.current[index - 1]?.focus();
      setFocusedIndex(index - 1);
    }
  };

  const handleFocus = (index: number) => {
    setFocusedIndex(index);
  };

  const renderInput = (index: number) => {
    return (
      <TextInput
        key={index}
        ref={ref => {
          if (ref) {
            inputRefs.current[index] = ref;
          }
        }}
        style={[
          styles.codeInputField,
          focusedIndex === index && styles.codeInputHighlight,
          error && styles.codeInputFieldError,
        ]}
        value={value[index] || ''}
        onChangeText={text => handleTextChange(text, index)}
        onKeyPress={({ nativeEvent }) => handleKeyPress(nativeEvent.key, index)}
        onFocus={() => handleFocus(index)}
        keyboardType="number-pad"
        maxLength={1}
        selectTextOnFocus
        autoFocus={index === 0}
      />
    );
  };

  return (
    <View style={[styles.container, containerStyle]}>
      <View style={styles.otpContainer}>
        {Array.from({ length: numberOfDigits }, (_, index) => renderInput(index))}
      </View>
      {error && <Text style={styles.errorText}>{error}</Text>}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
  },
  otpContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
    height: 60,
  },
  codeInputField: {
    width: 45,
    height: 55,
    borderWidth: 2,
    borderColor: Colors.border,
    borderRadius: BorderRadius.md,
    backgroundColor: Colors.white,
    color: Colors.text.primary,
    fontSize: 20,
    fontWeight: '600',
    textAlign: 'center',
  },
  codeInputFieldError: {
    borderColor: Colors.error,
  },
  codeInputHighlight: {
    borderColor: Colors.primary,
  },
  errorText: {
    ...Typography.caption,
    color: Colors.error,
    marginTop: Spacing.xs,
    textAlign: 'center',
  },
});
