export const ValidationRules = {
  required: (value: string) => {
    if (!value || value.trim() === '') {
      return 'This field is required';
    }
    return null;
  },

  email: (value: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(value)) {
      return 'Please enter a valid email address';
    }
    return null;
  },

  mobile: (value: string) => {
    const mobileRegex = /^[0-9]{10}$/;
    if (!mobileRegex.test(value)) {
      return 'Please enter a valid 10-digit mobile number';
    }
    return null;
  },

  name: (value: string) => {
    if (value.length < 2) {
      return 'Name must be at least 2 characters long';
    }
    if (!/^[a-zA-Z\s]+$/.test(value)) {
      return 'Name can only contain letters and spaces';
    }
    return null;
  },

  otp: (value: string) => {
    if (!/^[0-9]{6}$/.test(value)) {
      return 'Please enter a valid 6-digit OTP';
    }
    return null;
  },

  date: (value: string) => {
    const date = new Date(value);
    const today = new Date();
    const minAge = new Date();
    minAge.setFullYear(today.getFullYear() - 13); // Minimum age 13

    if (isNaN(date.getTime())) {
      return 'Please enter a valid date';
    }
    if (date > today) {
      return 'Birth date cannot be in the future';
    }
    if (date > minAge) {
      return 'You must be at least 13 years old';
    }
    return null;
  },
};

export const validateField = (value: string, rules: Array<(value: string) => string | null>): string | null => {
  for (const rule of rules) {
    const error = rule(value);
    if (error) {
      return error;
    }
  }
  return null;
};

export const validateForm = (fields: Record<string, { value: string; rules: Array<(value: string) => string | null> }>) => {
  const errors: Record<string, string> = {};
  let isValid = true;

  Object.keys(fields).forEach(key => {
    const field = fields[key];
    const error = validateField(field.value, field.rules);
    if (error) {
      errors[key] = error;
      isValid = false;
    }
  });

  return { isValid, errors };
};
