import { Platform } from 'react-native';

export const FontWeights = {
  regular: '400',
  medium: '500',
  semiBold: '600',
  bold: '700',
} as const;

export const FontFamily = {
  regular: Platform.select({
    ios: 'Roboto-Regular',
    android: 'Roboto-Regular',
  }),
  medium: Platform.select({
    ios: 'Roboto-Medium',
    android: 'Roboto-Medium',
  }),
  semiBold: Platform.select({
    ios: 'Roboto-SemiBold',
    android: 'Roboto-SemiBold',
  }),
  bold: Platform.select({
    ios: 'Roboto-Bold',
    android: 'Roboto-Bold',
  }),
} as const;

export const FontSizes = {
  xs: 12,
  sm: 14,
  base: 16,
  lg: 18,
  xl: 20,
  '2xl': 24,
  '3xl': 30,
  '4xl': 36,
  '5xl': 48,
} as const;

export const LineHeights = {
  xs: 16,
  sm: 20,
  base: 24,
  lg: 28,
  xl: 32,
  '2xl': 36,
  '3xl': 40,
  '4xl': 44,
  '5xl': 56,
} as const;

export const Typography = {
  h1: {
    fontFamily: FontFamily.bold,
    fontSize: FontSizes['4xl'],
    lineHeight: LineHeights['4xl'],
    fontWeight: FontWeights.bold,
  },
  h2: {
    fontFamily: FontFamily.bold,
    fontSize: FontSizes['3xl'],
    lineHeight: LineHeights['3xl'],
    fontWeight: FontWeights.bold,
  },
  h3: {
    fontFamily: FontFamily.semiBold,
    fontSize: FontSizes['2xl'],
    lineHeight: LineHeights['2xl'],
    fontWeight: FontWeights.semiBold,
  },
  h4: {
    fontFamily: FontFamily.semiBold,
    fontSize: FontSizes.xl,
    lineHeight: LineHeights.xl,
    fontWeight: FontWeights.semiBold,
  },
  h5: {
    fontFamily: FontFamily.medium,
    fontSize: FontSizes.lg,
    lineHeight: LineHeights.lg,
    fontWeight: FontWeights.medium,
  },
  h6: {
    fontFamily: FontFamily.medium,
    fontSize: FontSizes.base,
    lineHeight: LineHeights.base,
    fontWeight: FontWeights.medium,
  },
  body1: {
    fontFamily: FontFamily.regular,
    fontSize: FontSizes.base,
    lineHeight: LineHeights.base,
    fontWeight: FontWeights.regular,
  },
  body2: {
    fontFamily: FontFamily.regular,
    fontSize: FontSizes.sm,
    lineHeight: LineHeights.sm,
    fontWeight: FontWeights.regular,
  },
  caption: {
    fontFamily: FontFamily.regular,
    fontSize: FontSizes.xs,
    lineHeight: LineHeights.xs,
    fontWeight: FontWeights.regular,
  },
  button: {
    fontFamily: FontFamily.medium,
    fontSize: FontSizes.base,
    lineHeight: LineHeights.base,
    fontWeight: FontWeights.medium,
  },
} as const;
