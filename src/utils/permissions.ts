import { Platform, Alert, Linking } from 'react-native';
import {
  PERMISSIONS,
  RESULTS,
  request,
  check,
  openSettings,
  Permission,
} from 'react-native-permissions';

export const PermissionTypes = {
  CAMERA: Platform.select({
    ios: PERMISSIONS.IOS.CAMERA,
    android: PERMISSIONS.ANDROID.CAMERA,
  }) as Permission,
  PHOTO_LIBRARY: Platform.select({
    ios: PERMISSIONS.IOS.PHOTO_LIBRARY,
    android: PERMISSIONS.ANDROID.READ_EXTERNAL_STORAGE,
  }) as Permission,
  LOCATION: Platform.select({
    ios: PERMISSIONS.IOS.LOCATION_WHEN_IN_USE,
    android: PERMISSIONS.ANDROID.ACCESS_FINE_LOCATION,
  }) as Permission,
};

export const checkPermission = async (
  permission: Permission,
): Promise<boolean> => {
  try {
    const result = await check(permission);
    return result === RESULTS.GRANTED;
  } catch (error) {
    console.error('Error checking permission:', error);
    return false;
  }
};

export const requestPermission = async (
  permission: Permission,
): Promise<boolean> => {
  try {
    const result = await request(permission);
    return result === RESULTS.GRANTED;
  } catch (error) {
    console.error('Error requesting permission:', error);
    return false;
  }
};

export const requestLocationPermission = async (): Promise<boolean> => {
  const hasPermission = await checkPermission(PermissionTypes.LOCATION);

  if (hasPermission) {
    return true;
  }

  const granted = await requestPermission(PermissionTypes.LOCATION);

  if (!granted) {
    Alert.alert(
      'Location Permission Required',
      'Crime Lens needs location access to provide safety features. Please enable location permission in settings.',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Open Settings', onPress: () => openSettings() },
      ],
    );
  }

  return granted;
};

export const requestCameraPermission = async (): Promise<boolean> => {
  const hasPermission = await checkPermission(PermissionTypes.CAMERA);

  if (hasPermission) {
    return true;
  }

  const granted = await requestPermission(PermissionTypes.CAMERA);

  if (!granted) {
    Alert.alert(
      'Camera Permission Required',
      'Crime Lens needs camera access to take profile pictures. Please enable camera permission in settings.',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Open Settings', onPress: () => openSettings() },
      ],
    );
  }

  return granted;
};

export const requestPhotoLibraryPermission = async (): Promise<boolean> => {
  const hasPermission = await checkPermission(PermissionTypes.PHOTO_LIBRARY);

  if (hasPermission) {
    return true;
  }

  const granted = await requestPermission(PermissionTypes.PHOTO_LIBRARY);

  if (!granted) {
    Alert.alert(
      'Photo Library Permission Required',
      'Crime Lens needs photo library access to select profile pictures. Please enable photo library permission in settings.',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Open Settings', onPress: () => openSettings() },
      ],
    );
  }

  return granted;
};
