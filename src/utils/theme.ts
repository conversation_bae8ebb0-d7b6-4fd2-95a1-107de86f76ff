import { MD3LightTheme, configureFonts } from 'react-native-paper';

import { FontFamily } from './typography';

const fontConfig = {
  regular: {
    fontFamily: FontFamily.regular,
    fontWeight: '400' as const,
  },
  medium: {
    fontFamily: FontFamily.medium,
    fontWeight: '500' as const,
  },
  light: {
    fontFamily: FontFamily.regular,
    fontWeight: '300' as const,
  },
  thin: {
    fontFamily: FontFamily.regular,
    fontWeight: '100' as const,
  },
};

export const Colors = {
  primary: '#101E3D',
  primaryDark: '#0A1529',
  primaryLight: '#1A2E4D',
  secondary: '#42BAFF',
  secondaryDark: '#EA7621',
  secondaryLight: '#5CC7FF',
  accent: '#FFC107',
  background: '#FFFFFF',
  surface: '#F5F5F5',
  error: '#F44336',
  warning: '#FF9800',
  success: '#4CAF50',
  info: '#2196F3',
  text: {
    primary: '#212121',
    secondary: '#757575',
    disabled: '#BDBDBD',
    hint: '#9E9E9E',
  },
  border: '#E0E0E0',
  divider: '#EEEEEE',
  shadow: '#000000',
  white: '#FFFFFF',
  black: '#000000',
  transparent: 'transparent',
  overlay: 'rgba(0, 0, 0, 0.5)',
  // Safety-themed colors
  danger: '#D32F2F',
  safe: '#388E3C',
  caution: '#F57C00',
} as const;

export const theme = {
  ...MD3LightTheme,
  fonts: configureFonts({ config: fontConfig }),
  colors: {
    ...MD3LightTheme.colors,
    primary: Colors.primary,
    primaryContainer: Colors.primaryLight,
    secondary: Colors.secondary,
    secondaryContainer: Colors.secondaryLight,
    surface: Colors.surface,
    background: Colors.background,
    error: Colors.error,
    onPrimary: Colors.white,
    onSecondary: Colors.white,
    onSurface: Colors.text.primary,
    onBackground: Colors.text.primary,
    onError: Colors.white,
  },
};

export const Spacing = {
  xs: 4,
  sm: 8,
  md: 16,
  lg: 24,
  xl: 32,
  '2xl': 48,
  '3xl': 64,
} as const;

export const BorderRadius = {
  xs: 4,
  sm: 8,
  md: 12,
  lg: 16,
  xl: 24,
  full: 9999,
} as const;

export const Shadows = {
  sm: {
    shadowColor: Colors.shadow,
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.18,
    shadowRadius: 1.0,
    elevation: 1,
  },
  md: {
    shadowColor: Colors.shadow,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.23,
    shadowRadius: 2.62,
    elevation: 4,
  },
  lg: {
    shadowColor: Colors.shadow,
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.30,
    shadowRadius: 4.65,
    elevation: 8,
  },
} as const;
